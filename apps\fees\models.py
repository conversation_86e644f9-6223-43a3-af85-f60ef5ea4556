# D:\school_fees_saas_v2\apps\fees\models.py

import logging
from decimal import Decimal
from django.db import models
from django.db.models import Sum
from django.db.models.functions import Coalesce
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.conf import settings

from django.urls import reverse

# Assuming these are the correct locations for your models:
from apps.students.models import Student
from apps.schools.models import SchoolClass 
from apps.accounting.models import Account # Alias for clarity

from .utils import generate_invoice_number # For invoice numbering
from auditlog.registry import auditlog # For django-auditlog

from apps.schools.models import AcademicYear, Term # Assuming Term is also in schools
from apps.students.models import Student

from apps.schools.models import StaffUser

# Set up logging
logger = logging.getLogger(__name__)

# --- Fee Definition Models ---


class FeeHead(models.Model):
    name = models.CharField(max_length=150, unique=True, help_text=_("e.g., 'Tuition Fee', 'Bus Fee'"))
    description = models.TextField(blank=True, null=True)
    income_account_link = models.ForeignKey(
        Account,
        on_delete=models.PROTECT, # Use PROTECT to prevent accidental deletion of linked GL accounts
        null=True, blank=True,
        related_name='fee_heads',
        limit_choices_to={'account_type__classification': 'REVENUE'},
        verbose_name=_("Default Income Account")
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = _("Fee Head")
        verbose_name_plural = _("Fee Heads")

    def __str__(self):
        return self.name

class FeeStructure(models.Model):
    name = models.CharField(max_length=150, help_text=_("e.g., 'Grade 1 Fees 2024-25 Term 1'"))
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT, related_name='fee_structures')
    term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True, related_name='fee_structures')
    applicable_classes = models.ManyToManyField(SchoolClass, blank=True, related_name='fee_structures')
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), help_text=_("Total amount calculated from fee structure items"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-academic_year__start_date', 'term__start_date', 'name']
        unique_together = ['name', 'academic_year', 'term']
        verbose_name = _("Fee Structure")
        verbose_name_plural = _("Fee Structures")

    def __str__(self):
        term_info = f" - {self.term.name}" if self.term else " (Full Year)"
        return f"{self.name} ({self.academic_year.name}{term_info})"

    def save(self, *args, **kwargs):
        # Calculate total_amount from items if this is an update
        if self.pk:
            calculated_total = self.items.aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
            self.total_amount = calculated_total
        super().save(*args, **kwargs)

    def recalculate_total(self):
        """Recalculate and save the total amount from fee structure items"""
        self.total_amount = self.items.aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
        self.save(update_fields=['total_amount'])

class FeeStructureItem(models.Model):
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE, related_name='items')
    fee_head = models.ForeignKey(FeeHead, on_delete=models.PROTECT, related_name='structure_items')
    amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    description = models.CharField(max_length=255, blank=True, null=True)
    is_optional = models.BooleanField(default=False)

    class Meta:
        ordering = ['fee_head__name']
        unique_together = ['fee_structure', 'fee_head']
        verbose_name = _("Fee Structure Item")
        verbose_name_plural = _("Fee Structure Items")

    def __str__(self):
        return f"{self.fee_structure.name}: {self.fee_head.name} - {self.amount}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Recalculate fee structure total
        self.fee_structure.recalculate_total()

    def delete(self, *args, **kwargs):
        fee_structure = self.fee_structure
        super().delete(*args, **kwargs)
        # Recalculate fee structure total
        fee_structure.recalculate_total()


# class FeeHead(models.Model):
#     name = models.CharField(max_length=150, unique=True, help_text=_("Unique name for the fee category (e.g., 'Tuition Fee', 'Bus Fee'). Should be generic."))
#     description = models.TextField(blank=True, null=True, help_text=_("Optional description."))
#     # This income_account_link assumes FeeHead is a shared model. 
#     # If FeeHead is tenant-specific, then ChartOfAccount should be filtered by tenant or be schema-scoped.
#     income_account_link = models.ForeignKey(
#         Account,
#         on_delete=models.SET_NULL,
#         null=True, 
#         blank=True,
#         related_name='fee_heads_debiting_this_account', # Changed related_name for clarity
#         # limit_choices_to={'account_type__code': 'REVENUE', 'is_active': True}, # Filter by code
#         verbose_name=_("Default Income Account (CoA)")
#     )
#     is_active = models.BooleanField(default=True, help_text=_("Is this fee head currently active and available for use?")) # <<< ADD THIS FIELD
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['name']
#         verbose_name = _("Fee Head")
#         verbose_name_plural = _("Fee Heads")

#     def __str__(self):
#         return self.name

#     def clean(self):
#         if self.income_account_link and self.income_account_link.account_type.classification != 'REVENUE': # Check classification
#             raise ValidationError({'income_account_link': _("The selected account must be a Revenue type account.")})

# class FeeStructure(models.Model):
#     name = models.CharField(max_length=150, help_text=_("Descriptive name (e.g., 'Grade 1 Fees 2024-25 Term 1')."))
#     academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT, related_name='fee_structures')
#     term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True, related_name='fee_structures')
#     applicable_classes = models.ManyToManyField(
#         SchoolClass, 
#         blank=True, 
#         related_name='applicable_fee_structures',
#         help_text=_("Classes this fee structure applies to. Leave blank if applicable to all or defined by student group.")
#     ) # Renamed from your FeeStructure model's 'term' related field
#     description = models.TextField(blank=True, null=True)
#     is_active = models.BooleanField(default=True, help_text=_("Is this fee structure currently in use?"))
#     # total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), editable=False) # Auto-calculated
    
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['-academic_year__start_date', 'term__start_date', 'name']
#         unique_together = ['name', 'academic_year', 'term'] # Name should be unique within a year/term context
#         verbose_name = _("Fee Structure")
#         verbose_name_plural = _("Fee Structures")

#     def __str__(self):
#         year_name = getattr(self.academic_year, 'name', '?')
#         term_info = f" - {self.term.name}" if self.term else " (Full Year)"
#         return f"{self.name} ({year_name}{term_info})"

#     @property
#     def total_amount(self): # Changed to property
#         # Assuming related_name='items' on FeeStructureItem.fee_structure
#         total = self.items.aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
#         return total

#     def clean(self):
#         if self.term and self.academic_year_id and self.term.academic_year_id != self.academic_year_id:
#             raise ValidationError({'term': _("Selected term does not belong to the selected academic year.")})

#     # save() method is not strictly needed if total_amount is a property.
#     # If total_amount was a stored field, save would calculate and store it.

# class FeeStructureItem(models.Model): # Renamed from FeeStructureDetail for consistency if this is preferred
#     fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE, related_name='items')
#     fee_head = models.ForeignKey(FeeHead, on_delete=models.PROTECT, related_name='structure_items')
#     amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
#     description = models.CharField(max_length=255, blank=True, null=True, help_text=_("Optional override description for this item in this structure."))
#     is_optional = models.BooleanField(default=False, help_text=_("Can this item be opted out of by students?"))
#     # order = models.PositiveIntegerField(default=0)

#     class Meta:
#         ordering = ['fee_head__name']
#         unique_together = ['fee_structure', 'fee_head'] # Ensure a fee head is not added twice to the same structure
#         verbose_name = _("Fee Structure Item")
#         verbose_name_plural = _("Fee Structure Items")

#     def __str__(self):
#         return f"{self.fee_structure.name}: {self.fee_head.name} - {self.amount}"


# --- Student Specific Fee Models ---

class StudentFeeAllocation(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='fee_allocations') # Renamed related_name
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.PROTECT, related_name='student_allocations')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT, related_name='student_fee_allocations') # Clarified related_name
    term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True, related_name='student_fee_allocations_term')
    
    is_active = models.BooleanField(default=True, help_text=_("Is this allocation currently active for billing?"))
    notes = models.TextField(_("Notes/Reason for Allocation"), blank=True, null=True, help_text=_("Any specific notes for this student's fee structure allocation."))
    # Removed description, using notes instead.
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name="created_fee_allocations")


    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['student', 'fee_structure', 'term'], # Term being part of key makes it unique per term if term is not null
                name='unique_student_structure_term_allocation',
                condition=models.Q(term__isnull=False)
            ),
            models.UniqueConstraint(
                fields=['student', 'fee_structure'], # If term is null, means it's for the whole year linked via fee_structure's year
                name='unique_student_structure_allocation_year',
                condition=models.Q(term__isnull=True)
            )
        ]
        verbose_name = _("Student Fee Structure Allocation")
        verbose_name_plural = _("Student Fee Structure Allocations")
        ordering = ['student__last_name', '-academic_year__start_date', 'term__start_date']


    def __str__(self):
        term_name = f" - {self.term.name}" if self.term else " (Annual)"
        return f"{self.student.get_full_name()} - {self.fee_structure.name}{term_name} ({self.academic_year.name})"

    def clean(self):
        if self.fee_structure_id and self.academic_year_id and self.fee_structure.academic_year_id != self.academic_year_id:
            raise ValidationError(_("The academic year of the fee structure must match the academic year of the allocation."))
        if self.fee_structure_id and self.term_id and self.fee_structure.term_id and self.fee_structure.term_id != self.term_id:
            raise ValidationError(_("The term of the fee structure must match the term of the allocation if both are set."))
        if self.fee_structure_id and not self.fee_structure.is_active:
            raise ValidationError(_("Cannot allocate an inactive fee structure."))


class ConcessionType(models.Model): # Renamed from Concession for clarity
    CONCESSION_TYPE_CHOICES = [
        ('PERCENTAGE', _('Percentage')),
        ('FIXED_AMOUNT', _('Fixed Amount')),
    ]
    name = models.CharField(max_length=150, unique=True, help_text=_("Name of the concession (e.g., 'Sibling Discount', 'Staff Child Waiver')."))
    description = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=20, choices=CONCESSION_TYPE_CHOICES, default='PERCENTAGE')
    value = models.DecimalField(
        max_digits=10, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_("Percentage (e.g., 10 for 10%) or Fixed Amount.")
    )
    # applicable_to_all_fee_heads = models.BooleanField(default=True, help_text="Does this concession apply to all fee heads, or specific ones?")
    # specific_fee_heads = models.ManyToManyField(FeeHead, blank=True, help_text="If not applicable to all, select specific fee heads.")
    # The above M2M makes it complex. Simpler: concession applies to invoice total or specific StudentFeeAllocation.

    is_active = models.BooleanField(default=True, help_text=_("Is this concession type currently available?"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = _("Concession Type")
        verbose_name_plural = _("Concession Types")

    def __str__(self):
        if self.type == 'PERCENTAGE':
            return f"{self.name} ({self.value}%)"
        return f"{self.name} (Fixed {self.value})"

    def clean(self):
        if self.type == 'PERCENTAGE' and (self.value <= 0 or self.value > 100):
            raise ValidationError({'value': _("Percentage must be between 0 (exclusive) and 100 (inclusive).")})
        if self.type == 'FIXED_AMOUNT' and self.value <= 0:
            raise ValidationError({'value': _("Fixed amount must be greater than 0.")})

    def calculate_discount_on_amount(self, base_amount):
        base_amount = Decimal(base_amount)
        if not self.is_active: return Decimal('0.00')
        
        if self.type == 'PERCENTAGE':
            discount = (base_amount * self.value) / Decimal('100.00')
        elif self.type == 'FIXED_AMOUNT':
            discount = self.value
        else:
            discount = Decimal('0.00')
        return min(discount, base_amount).quantize(Decimal('0.01'))


class StudentConcession(models.Model): # This links a ConcessionType to a student
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='applied_concessions') # Renamed related_name
    concession_type = models.ForeignKey(ConcessionType, on_delete=models.PROTECT, limit_choices_to={'is_active': True})
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT) # Concession applied for this year
    term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True) # Optional: if concession is term-specific
    
    # Optional: Apply to a specific FeeHead or FeeStructureItem for this student
    # specific_fee_structure_item = models.ForeignKey(FeeStructureItem, on_delete=models.CASCADE, null=True, blank=True)
    # specific_fee_head = models.ForeignKey(FeeHead, on_delete=models.CASCADE, null=True, blank=True)
    # If these are null, concession applies to total applicable fees.

    # Optional: override the value/percentage for this student's specific instance of the concession
    # override_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    # override_type = models.CharField(max_length=20, choices=ConcessionType.CONCESSION_TYPE_CHOICES, null=True, blank=True)

    notes = models.TextField(blank=True, null=True)
    granted_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name="granted_concessions")
    granted_at = models.DateTimeField(default=timezone.now) # Changed to default=timezone.now
    
    class Meta:
        unique_together = [['student', 'concession_type', 'academic_year', 'term']] # Allow term to be part of uniqueness if used
        ordering = ['student__last_name', '-academic_year__start_date', 'concession_type__name']
        verbose_name = _("Student Specific Concession")
        verbose_name_plural = _("Student Specific Concessions")

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.concession_type.name} ({self.academic_year.name})"

    # def get_effective_discount_value(self, base_amount):
    #     """Calculates discount using overrides if present, else concession_type's default."""
    #     # This logic is complex if you allow overrides. For now, assume it uses concession_type directly.
    #     return self.concession_type.calculate_discount_on_amount(base_amount)

# --- Invoicing Models ---

class InvoiceStatus(models.TextChoices): # Moved choices here for clarity
    DRAFT = 'DRAFT', _('Draft')
    SENT = 'SENT', _('Sent / Unpaid')
    PARTIALLY_PAID = 'PARTIALLY_PAID', _('Partially Paid')
    PAID = 'PAID', _('Paid')
    OVERDUE = 'OVERDUE', _('Overdue')
    VOID = 'VOID', _('Void')
    CANCELLED = 'CANCELLED', _('Cancelled') # Same as VOID, or different meaning? Using VOID for now.


class Invoice(models.Model):
    class InvoiceStatus(models.TextChoices):
        DRAFT = 'DRAFT', _('Draft')
        SENT = 'SENT', _('Sent')
        PARTIALLY_PAID = 'PARTIALLY_PAID', _('Partially Paid')
        PAID = 'PAID', _('Paid')
        OVERDUE = 'OVERDUE', _('Overdue')
        VOID = 'VOID', _('Void')
        CANCELLED = 'CANCELLED', _('Cancelled')

    student = models.ForeignKey(Student, on_delete=models.PROTECT, related_name='invoices')
    invoice_number = models.CharField(max_length=50, unique=True, blank=True, db_index=True)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT)
    term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True)
    issue_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    subtotal_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_concession_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    status = models.CharField(max_length=20, choices=InvoiceStatus.choices, default=InvoiceStatus.DRAFT, db_index=True)
    notes_to_parent = models.TextField(blank=True, null=True)
    internal_notes = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(StaffUser, related_name='created_invoices', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-issue_date', '-id']
        verbose_name = _("Invoice")
        verbose_name_plural = _("Invoices")

    def __str__(self):
        return f"{self.invoice_number or f'Draft-{self.pk}'} - {self.student.get_full_name()}"


    # --- ADD THIS METHOD ---
    def get_absolute_url(self):
        """
        Returns the canonical URL for an instance of the model.
        This should point to the invoice's detail view.
        """
        # Assumes you have a URL pattern named 'invoice_detail' in your 'fees' app
        # that takes the invoice's pk as an argument.
        return reverse('fees:invoice_detail', kwargs={'pk': self.pk})
    # --- END ADD METHOD ---
    
    
    @property
    def invoice_number_display(self):
        return self.invoice_number or f"Draft-{self.pk}"

    @property
    def total_amount(self):
        """Total billable amount (subtotal - concessions)."""
        return self.subtotal_amount - self.total_concession_amount

    @property
    def net_billable_amount(self):
        """Net billable amount (same as total_amount, for compatibility)."""
        return self.subtotal_amount - self.total_concession_amount

    @property
    def balance_due(self):
        return self.total_amount - self.amount_paid

    @property
    def is_overdue(self):
        return self.due_date < timezone.now().date() and self.status not in [self.InvoiceStatus.PAID, self.InvoiceStatus.VOID, self.InvoiceStatus.CANCELLED]

    def save(self, *args, **kwargs):
        """
        Custom save method to handle invoice number generation for new invoices.
        """
        is_new = self._state.adding

        # Generate invoice number for new invoices if not provided
        if is_new and not self.invoice_number:
            try:
                # Get the current tenant (School object)
                from django.db import connection
                tenant = getattr(connection, 'tenant', None)

                if tenant:
                    # Generate invoice number using the utility function
                    self.invoice_number = generate_invoice_number(tenant)
                    logger.info(f"Generated invoice number {self.invoice_number} for new invoice")
                else:
                    # Fallback: generate a simple invoice number based on timestamp
                    import time
                    timestamp = int(time.time() * 1000000)  # microseconds for better uniqueness
                    self.invoice_number = f"INV-{timestamp}"
                    logger.warning(f"No tenant context available, using fallback invoice number: {self.invoice_number}")

            except Exception as e:
                logger.error(f"Error generating invoice number: {e}")
                # Use a fallback number to prevent save failure
                import time
                timestamp = int(time.time() * 1000000)
                self.invoice_number = f"ERR-{timestamp}"

        super().save(*args, **kwargs)

    def recalculate_totals_from_details(self):
        """
        Recalculates subtotal and concession amounts from related details and saves.
        This is the primary method to call when invoice line items change.
        """
        details = self.details.all()
        positive_lines_total = sum(d.amount for d in details if d.amount > 0)
        negative_lines_total = sum(d.amount for d in details if d.amount < 0)

        self.subtotal_amount = positive_lines_total
        self.total_concession_amount = abs(negative_lines_total)
        
        # After updating amounts, also update status
        self.update_status_based_on_balance()
        
        self.save(update_fields=['subtotal_amount', 'total_concession_amount', 'status'])
        logger.info(f"Recalculated totals for Invoice PK {self.pk}. New Subtotal: {self.subtotal_amount}, Concessions: {self.total_concession_amount}, Status: {self.status}")

    def update_status_based_on_balance(self):
        """
        Updates the invoice status based on its current amounts.
        This method does NOT save; it just sets the status field.
        """
        # Do not change status of manually set terminal states
        if self.status in [self.InvoiceStatus.DRAFT, self.InvoiceStatus.CANCELLED, self.InvoiceStatus.VOID]:
            return

        current_balance = self.total_amount - self.amount_paid
        
        if current_balance <= Decimal('0.00'):
            self.status = self.InvoiceStatus.PAID
        elif self.amount_paid > Decimal('0.00'):
            self.status = self.InvoiceStatus.PARTIALLY_PAID
        elif self.due_date and self.due_date < timezone.now().date():
            self.status = self.InvoiceStatus.OVERDUE
        else:
            self.status = self.InvoiceStatus.SENT

# class Invoice(models.Model):
#     student = models.ForeignKey(Student, on_delete=models.PROTECT, related_name='student_invoices') # Changed related_name
#     invoice_number = models.CharField(max_length=50, unique=True, blank=True, db_index=True, help_text=_("Unique invoice number, auto-generated on first save if blank."))
    
#     academic_year = models.ForeignKey(AcademicYear, on_delete=models.PROTECT) # Invoice must belong to an AY
#     term = models.ForeignKey(Term, on_delete=models.PROTECT, null=True, blank=True) # Optional

#     # Link to the source of generation, if applicable
#     # student_fee_allocation = models.ForeignKey(StudentFeeAllocation, on_delete=models.SET_NULL, null=True, blank=True, related_name='generated_invoices')
#     # fee_structure_used = models.ForeignKey(FeeStructure, on_delete=models.SET_NULL, null=True, blank=True)

#     issue_date = models.DateField(default=timezone.now)
#     due_date = models.DateField()
    
#     # Financials
#     subtotal_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), help_text=_("Sum of all positive line item amounts."))
#     total_concession_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), help_text=_("Sum of all concession/discount line items or applied concessions."))
#     # net_amount = total_amount - discount_applied (as per your old model)
#     # My new model uses subtotal_amount - total_concession_amount for net_billable
    
#     # Stored values, updated by triggers or specific actions
#     amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
#     status = models.CharField(max_length=20, choices=InvoiceStatus.choices, default=InvoiceStatus.DRAFT, db_index=True)
    
#     notes_to_parent = models.TextField(blank=True, null=True, help_text=_("Visible to parent on the invoice."))
#     internal_notes = models.TextField(blank=True, null=True, help_text=_("For administrative use only."))

#     # Journal Entry Link (Important for accounting integration)
#     # This should be set when the invoice is "posted" to accounting (i.e., status changes from DRAFT)
#     # journal_entry = models.OneToOneField('accounting.JournalEntry', on_delete=models.SET_NULL, null=True, blank=True, related_name='source_invoice')

#     created_by = models.ForeignKey(
#         StaffUser, 
#         related_name='created_invoices_staff', 
#         on_delete=models.SET_NULL, 
#         null=True, 
#         blank=True) # Clarified related_name
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
    
#     # Fields for voiding/cancellation
#     # cancelled_at = models.DateTimeField(null=True, blank=True, editable=False)
#     # cancellation_reason = models.TextField(blank=True, null=True)
#     # void_reason = models.TextField(blank=True, null=True)
#     # voided_at = models.DateTimeField(null=True, blank=True)
#     # voided_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='voided_invoices', on_delete=models.SET_NULL, null=True, blank=True)


#     class Meta:
#         ordering = ['-issue_date', '-id']
#         verbose_name = _("Invoice")
#         verbose_name_plural = _("Invoices")

#     def __str__(self):
#         return f"{self.invoice_number or f'Draft INV_PK{self.pk}'} - {self.student.get_full_name()}"

#     def get_absolute_url(self):
#         """Returns the URL to view this invoice's detail page"""
#         from django.urls import reverse
#         return reverse('fees:invoice_detail', kwargs={'pk': self.pk})

#     @property
#     def invoice_number_display(self):
#         """Display invoice number or fallback for drafts"""
#         return self.invoice_number or f"Draft-{self.pk}"

#     @property
#     def total_amount(self):
#         """Total invoice amount before any payments (subtotal - concessions)"""
#         return self.net_billable_amount

#     @property
#     def discount_applied(self):
#         """Total discount/concession amount applied to this invoice"""
#         return self.total_concession_amount

#     @property
#     def net_billable_amount(self): # Total to be paid after all line items and concessions
#         return self.subtotal_amount - self.total_concession_amount

#     @property
#     def balance_due(self):
#         return self.net_billable_amount - self.amount_paid
    
#     # --- ADD THIS NEW METHOD ---
#     def update_status_based_on_balance(self):
#         """
#         Updates the invoice status based on its current balance.
#         This method does NOT save the object.
#         """
#         # Do not change status of manually set terminal states
#         if self.status in [self.InvoiceStatus.DRAFT, self.InvoiceStatus.CANCELLED, self.InvoiceStatus.VOID]:
#             return

#         current_balance = self.balance_due # Use the property

#         if current_balance <= Decimal('0.00'):
#             self.status = self.InvoiceStatus.PAID
#         elif self.amount_paid > Decimal('0.00'):
#             self.status = self.InvoiceStatus.PARTIALLY_PAID
#         # Check for overdue only if there's a balance
#         elif self.due_date and self.due_date < timezone.now().date():
#             self.status = self.InvoiceStatus.OVERDUE
#         else:
#             # If there's a balance but it's not partially paid or overdue, it must be just 'SENT'
#             self.status = self.InvoiceStatus.SENT

#     @property
#     def is_overdue(self):
#         return self.due_date < timezone.now().date() and self.status not in [InvoiceStatus.PAID, InvoiceStatus.VOID, InvoiceStatus.CANCELLED]

#     @property
#     def is_editable(self):
#         """Check if invoice can be edited (only DRAFT invoices are editable)"""
#         return self.status == InvoiceStatus.DRAFT

#     @property
#     def is_payable(self):
#         """Check if invoice can receive payments"""
#         return self.status in [InvoiceStatus.SENT, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE] and self.balance_due > 0

#     def _generate_invoice_number_on_issue(self, tenant): # Pass tenant
#         if not self.invoice_number: # Only generate if not already set
#             self.invoice_number = generate_invoice_number(tenant) # Use the utility
#             # self.save(update_fields=['invoice_number']) # Save immediately or let the calling view save
    
#     def update_amounts_and_status(self, payment_date_for_overdue_check=None):
#         """Recalculates totals from details and then updates status. Call after details/concessions change."""
#         positive_lines_total = self.details.filter(amount__gte=0).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
#         negative_lines_total = self.details.filter(amount__lt=0).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
        
#         self.subtotal_amount = positive_lines_total
#         self.total_concession_amount = abs(negative_lines_total) # Store concessions as positive

#         # Update Status
#         current_status = self.status
#         if current_status == InvoiceStatus.VOID: # or InvoiceStatus.CANCELLED
#             return False # Don't change status if already voided/cancelled

#         new_status = current_status
#         calculated_due = self.net_billable_amount - self.amount_paid # Use current amount_paid

#         if calculated_due <= Decimal('0.00'):
#             new_status = InvoiceStatus.PAID
#         elif self.amount_paid > Decimal('0.00'):
#             new_status = InvoiceStatus.PARTIALLY_PAID
#         else: # Nothing paid, and net_billable > 0
#             # Preserve DRAFT status if invoice hasn't been issued yet
#             if current_status == InvoiceStatus.DRAFT:
#                 new_status = InvoiceStatus.DRAFT
#             else:
#                 new_status = InvoiceStatus.SENT

#         # Overdue check only if not DRAFT, PAID, or VOID
#         if new_status not in [InvoiceStatus.DRAFT, InvoiceStatus.PAID, InvoiceStatus.VOID]:
#             today = payment_date_for_overdue_check or timezone.now().date()
#             if self.due_date < today:
#                 new_status = InvoiceStatus.OVERDUE
        
#         status_changed = (new_status != self.status)
#         self.status = new_status
        
#         # If called from save(), save will happen. If called standalone, caller should save.
#         # self.save(update_fields=['subtotal_amount', 'total_concession_amount', 'status'])
#         return status_changed


#     def save(self, *args, **kwargs):
#         # total/status updates are better handled by signals from InvoiceDetail or specific actions (like RecordPayment)
#         # to avoid recursion or complex save logic here.
#         # However, if invoice_number generation depends on save(), it needs to be here.

#         is_new = self._state.adding
#         # If issue_date or due_date are not set, provide sensible defaults
        
#         original_invoice_number_on_instance = self.invoice_number # Capture before any changes
        
#         logger.info(f"Invoice Save - START. PK: {self.pk}, Is New: {is_new}, Original Invoice #: '{original_invoice_number_on_instance}', Student PK: {self.student_id}")
        
#         if not self.issue_date: self.issue_date = timezone.now().date()
#         if not self.due_date: self.due_date = self.issue_date + timezone.timedelta(days=30) # Default 30 days due

#         # Generate invoice number BEFORE saving if it's a new invoice
#         if is_new and not self.invoice_number:
#             max_retries = 3
#             retry_count = 0

#             while retry_count < max_retries:
#                 try:
#                     # Get the current tenant from django-tenants context
#                     from django_tenants.utils import get_tenant_model
#                     from django.db import connection, transaction

#                     # Get the current tenant from the connection
#                     if hasattr(connection, 'tenant') and connection.tenant:
#                         tenant_obj = connection.tenant
#                         logger.info(f"Invoice Save: Tenant '{tenant_obj.schema_name}' found on connection.")

#                         # Get the actual School object from the tenant
#                         from apps.tenants.models import School
#                         try:
#                             school_obj = School.objects.get(schema_name=tenant_obj.schema_name)
#                             logger.info(f"Invoice Save: Found School object: {school_obj}")

#                             # Use atomic transaction for invoice number generation
#                             with transaction.atomic():
#                                 self._generate_invoice_number_on_issue(school_obj) # Pass School object

#                                 # Check if this invoice number already exists (race condition check)
#                                 if Invoice.objects.filter(invoice_number=self.invoice_number).exists():
#                                     logger.warning(f"Invoice Save: Generated number {self.invoice_number} already exists, retrying...")
#                                     self.invoice_number = None  # Reset to try again
#                                     retry_count += 1
#                                     continue
#                                 else:
#                                     break  # Success, exit retry loop
#                         except Exception as school_error:
#                             logger.error(f"Invoice Save: Could not get School object: {school_error}")
#                             raise school_error
#                     else:
#                         logger.warning("Invoice Save: No tenant on connection. Cannot auto-generate tenant-specific invoice number reliably via utility.")
#                         # Fallback: generate a simple invoice number based on timestamp
#                         import time
#                         timestamp = int(time.time() * 1000000)  # microseconds for better uniqueness
#                         simple_invoice_number = f"INV-{timestamp}"
#                         self.invoice_number = simple_invoice_number
#                         logger.info(f"Invoice Save: Using fallback number: {self.invoice_number}")
#                         break

#                 except Exception as e:
#                     logger.error(f"Invoice Save: Error during invoice number generation (attempt {retry_count + 1}): {e}", exc_info=True)
#                     retry_count += 1

#                     if retry_count >= max_retries:
#                         # Final fallback if all retries failed
#                         logger.warning(f"Invoice Save: All retries failed, using timestamp fallback")
#                         import time
#                         timestamp = int(time.time() * 1000000)  # microseconds
#                         simple_invoice_number = f"INV-{timestamp}"
#                         self.invoice_number = simple_invoice_number
#                         break
        
#         logger.info(f"Invoice Save: About to call super().save(). Invoice #: '{self.invoice_number}', Student PK: {self.student_id}")
        
#         super().save(*args, **kwargs) # Save with the generated invoice number
        
#         logger.info(f"Invoice Save - END. Successfully saved Invoice PK {self.pk} with number '{self.invoice_number}'")
        


class LineTypeChoices(models.TextChoices):
    FEE_ITEM = 'FEE_ITEM', _('Fee Item / Charge')
    CONCESSION = 'CONCESSION_ITEM', _('Concession / Discount Applied')
    # TAX_ITEM = 'TAX_ITEM', _('Tax Item') # For future

class InvoiceDetail(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='details')
    
    line_type = models.CharField(
        max_length=20, 
        choices=LineTypeChoices.choices, 
        default=LineTypeChoices.FEE_ITEM)
    
    fee_head = models.ForeignKey(FeeHead, on_delete=models.PROTECT, null=True, blank=True, help_text=_("Link to FeeHead if this is a standard charge/fee."))
    concession_type = models.ForeignKey(ConcessionType, on_delete=models.PROTECT, null=True, blank=True, help_text=_("Link to ConcessionType if this is a discount line."))
    
    description = models.CharField(max_length=255, help_text=_("Description of the charge or concession."))
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'), validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, help_text=_("Price per unit. For concessions, this might be the discount amount per unit if quantity > 1."))
    
    # Amount for this line. For FEE_ITEM, it's positive. For CONCESSION_ITEM, it should be negative.
    amount = models.DecimalField(max_digits=12, decimal_places=2, help_text=_("Total for this line (Quantity * Unit Price). Negative for concessions."))

    # Optional: Link to specific income/expense account for this line item (overrides FeeHead's default)
    # specific_gl_account = models.ForeignKey(ChartOfAccount, on_delete=models.SET_NULL, null=True, blank=True)

    # Optional: if a concession line applies to a specific charge line on the same invoice
    # applies_to_line = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='applied_by_concessions')

    # Timestamp fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['id']
        verbose_name = _("Invoice Line Item")
        verbose_name_plural = _("Invoice Line Items")

    def __str__(self):
        return f"{self.description} - {self.amount} (Invoice: {self.invoice_id})"

    def save(self, *args, **kwargs):
        if self.line_type == LineTypeChoices.FEE_ITEM:
            self.amount = (self.quantity * self.unit_price).quantize(Decimal('0.01'))
            if self.amount < 0: self.amount = abs(self.amount) # Ensure fee items are positive
        elif self.line_type == LineTypeChoices.CONCESSION:
            self.amount = (self.quantity * self.unit_price).quantize(Decimal('0.01'))
            if self.amount > 0: self.amount = -abs(self.amount) # Ensure concession amounts are negative
        
        super().save(*args, **kwargs)
        # No need to call invoice.update_total_amount() here;
        # better to do it in Invoice.save() or via a signal from InvoiceDetail post_save.
        # For simplicity now, Invoice.update_amounts_and_status() will be called by view/action.

# --- Auditlog Registration ---
# Ensure these models are correctly defined above before registering.
# auditlog.register(AcademicYear)
# auditlog.register(Term)
auditlog.register(FeeHead)
auditlog.register(FeeStructure)
auditlog.register(FeeStructureItem) # Or FeeStructureDetail if you use that name
auditlog.register(StudentFeeAllocation)
auditlog.register(ConcessionType) # Or Concession if you use that name
auditlog.register(StudentConcession)
auditlog.register(Invoice)
auditlog.register(InvoiceDetail)

