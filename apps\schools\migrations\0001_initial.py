# Generated by Django 5.1.9 on 2025-06-18 20:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grading_system', models.CharField(choices=[('PERCENTAGE', 'Percentage (0-100%)'), ('GPA_4', 'GPA Scale (0-4.0)'), ('GPA_5', 'GPA Scale (0-5.0)'), ('LETTER', 'Letter Grades (A, B, C...)'), ('CUSTOM', 'Custom/Narrative')], default='PERCENTAGE', help_text='The primary grading system used by the school.', max_length=20)),
            ],
            options={
                'verbose_name': 'Academic Setting',
                'verbose_name_plural': 'Academic Settings',
            },
        ),
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='e.g., 2024-2025, 2025', max_length=100, unique=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=False, help_text='Is this the currently active academic year? Only one should be active.')),
                ('is_current', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Academic Year',
                'verbose_name_plural': 'Academic Years',
                'ordering': ['-start_date', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3)),
            ],
        ),
        migrations.CreateModel(
            name='SchoolClass',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='The name of the class (e.g., Grade 1, Form IV A, Senior KG).', max_length=100, verbose_name='class name')),
                ('description', models.TextField(blank=True, help_text='Optional: A brief description of the class or its focus.', null=True, verbose_name='description')),
                ('is_active', models.BooleanField(db_index=True, default=True, help_text='Is this class currently active and can have students enrolled or be used in new records?', verbose_name='is active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='last updated at')),
            ],
            options={
                'verbose_name': 'School Class',
                'verbose_name_plural': 'School Classes',
                'ordering': ['name'],
            },
        ),
    ]
