# Generated by Django 5.1.9 on 2025-07-03 13:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0006_remove_schoolprofile_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReceiptSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prefix', models.CharField(default='RCT-', help_text="Prefix for receipt numbers (e.g., 'RCT-', 'RCPT-').", max_length=10)),
                ('last_number', models.PositiveIntegerField(default=0, help_text='The last sequential number used.')),
                ('padding_digits', models.PositiveSmallIntegerField(default=9, help_text='Number of digits to pad the sequence number (e.g., 9 for 13-digit format).')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='When this sequence was last updated.')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this sequence was created.')),
            ],
            options={
                'verbose_name': 'Receipt Sequence',
                'verbose_name_plural': 'Receipt Sequences',
            },
        ),
    ]
