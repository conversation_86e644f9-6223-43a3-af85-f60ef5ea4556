# Generated by Django 5.1.9 on 2025-06-27 09:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0005_seed_account_types'),
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paymentmethod',
            name='linked_account',
            field=models.ForeignKey(blank=True, help_text='The Cash, Bank, or Payment Gateway Clearing account in CoA.', limit_choices_to={'account_type__classification': 'ASSET', 'account_type__normal_balance': 'DEBIT'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payment_methods_linked', to='accounting.account'),
        ),
    ]
