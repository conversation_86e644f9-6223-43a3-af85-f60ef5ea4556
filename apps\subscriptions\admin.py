# D:\school_fees_saas_v2\apps\subscriptions\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _ # For custom display names
from .models import SubscriptionPlan, Feature, Subscription

@admin.register(Feature)
class FeatureAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'description')
    search_fields = ('name', 'code')
    ordering = ('name',)

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'slug', # Added from your model
        'price_monthly',
        'price_annually',
        'trial_period_days', # Added from your model
        'max_students',
        'max_staff',
        'is_active',
        'is_public',
        'display_order', # Added from your model
        'updated_at'
    )
    list_filter = ('is_active', 'is_public', 'trial_period_days', 'features')
    search_fields = ('name', 'description', 'slug')
    ordering = ('display_order', 'price_monthly', 'name')
    filter_horizontal = ('features',) # Good for M2M
    prepopulated_fields = {'slug': ('name',)} # Auto-generate slug from name

    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'description', 'is_active', 'is_public', 'display_order')
        }),
        (_('Pricing & Trial'), { # Grouped pricing and trial
            'fields': ('price_monthly', 'price_annually', 'trial_period_days', 
                        'pg_price_id_monthly', 'pg_price_id_annually')
        }),
        (_('Usage Limits'), {
            'fields': ('max_students', 'max_staff') # Add other limits like max_storage_gb if you have them
        }),
        (_('Features Included'), {
            'fields': ('features',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',) # Good to collapse
        }),
    )
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'school_display', # Use the custom method name
        'plan_display',   # Use the custom method name
        'status',
        'billing_cycle',
        'current_period_start',
        'current_period_end',
        'trial_end_date',
        'is_usable_admin', # Use the custom method name
        'updated_at'
    )
    list_filter = (
        'status', 
        'plan', 
        'billing_cycle', 
        'school__is_active', # This is good if School has is_active
        'current_period_end', # Corrected - this is a date field for filtering
        'trial_end_date',
        'cancel_at_period_end', # Good filter
        'created_at'
    )
    
    search_fields = (
        'school__name', 
        'school__schema_name',
        'plan__name',
        'pg_subscription_id',
        'pg_customer_id'
    )
    ordering = ('-updated_at', 'school__name')
    # Make fields managed by payment gateway/webhooks readonly
    readonly_fields = (
        'created_at', 'updated_at', 
        'pg_subscription_id', 'pg_customer_id',
        'trial_start_date', # Usually set programmatically
        'current_period_start', # Usually set by gateway/logic
        # 'current_period_end', # Might be editable for manual adjustments in some cases
        'cancelled_at', 'ended_at',
        'is_usable_admin' # Display method is readonly
    )
    autocomplete_fields = ['school', 'plan'] # Good for selection with many schools/plans

    fieldsets = (
        (_("Core Subscription Info"), { # More descriptive title
            'fields': ('school', 'plan', 'billing_cycle', 'price_at_subscription', 'notes')
        }),
        (_('Status & Key Dates'), {
            'fields': ('status', 'trial_start_date', 'trial_end_date', 
                        'current_period_start', 'current_period_end', 'is_usable_admin')
        }),
        (_('Payment Gateway Integration (Read-Only)'), { # Clarify these are usually read-only here
            'fields': ('pg_customer_id', 'pg_subscription_id'),
            'classes': ('collapse',) 
        }),
        (_('Cancellation & End'), { # Grouped these
            'fields': ('cancel_at_period_end', 'cancelled_at', 'ended_at'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    @admin.display(description=_('School'), ordering='school__name') # Use _ for translation
    def school_display(self, obj): # Changed name to avoid conflict with model field
        return obj.school.name if obj.school else _("N/A")

    @admin.display(description=_('Plan'), ordering='plan__name')
    def plan_display(self, obj): # Changed name
        return obj.plan.name if obj.plan else _("N/A")

    @admin.display(description=_('Is Usable?'), boolean=True)
    def is_usable_admin(self, obj): # Changed name to avoid conflict with property
        return obj.is_usable


