# Generated by Django 5.1.9 on 2025-06-30 21:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0005_seed_account_types'),
        ('fees', '0003_feestructure_total_amount'),
        ('payments', '0002_fix_payment_method_limit_choices'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paymentallocation',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='allocations', to='fees.invoice'),
        ),
        migrations.AlterField(
            model_name='paymentmethod',
            name='linked_account',
            field=models.ForeignKey(blank=True, help_text='The Cash, Bank, or Payment Gateway Clearing account in CoA.', limit_choices_to={'account_type__classification': 'ASSET', 'account_type__normal_balance': 'DR'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payment_methods_linked', to='accounting.account'),
        ),
    ]
