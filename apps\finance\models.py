# D:\school_fees_saas_V2\apps\finance\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings # For recorded_by user on Expense
from django.utils import timezone
from decimal import Decimal
from django.core.validators import MinValueValidator

from apps.common.models import BaseModel # Assuming you have this
from apps.accounting.models import Account# For linking
from apps.fees.models import AcademicYear, Term # For BudgetAmount
# Import PaymentMethod if Expense model links to it directly
from apps.payments.models import PaymentMethod

class ExpenseCategory(BaseModel):
    name = models.CharField(max_length=100, unique=True, help_text=_("e.g., Office Supplies, Utilities, Salaries"))
    description = models.TextField(blank=True, null=True)
    # Each expense category should map to a specific expense account in CoA
    expense_account = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        related_name='expense_categories',
        limit_choices_to={'account_type': 'EXPENSE'},
        help_text=_("The Expense account in CoA this category posts to.")
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("Expense Category")
        verbose_name_plural = _("Expense Categories")
        ordering = ['name']

    def __str__(self):
        return self.name

class Vendor(BaseModel):
    name = models.CharField(max_length=150, unique=True, help_text=_("Name of the supplier or vendor."))
    contact_person = models.CharField(max_length=100, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    phone_number = models.CharField(max_length=30, blank=True, null=True)
    address_line1 = models.CharField(max_length=255, blank=True, null=True)
    address_line2 = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state_province = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("Vendor/Supplier")
        verbose_name_plural = _("Vendors/Suppliers")
        ordering = ['name']

    def __str__(self):
        return self.name

class Expense(BaseModel):
    expense_date = models.DateField(default=timezone.now)
    category = models.ForeignKey(ExpenseCategory, on_delete=models.PROTECT, related_name='expenses')
    vendor = models.ForeignKey(Vendor, on_delete=models.SET_NULL, null=True, blank=True, related_name='expenses')
    amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    description = models.TextField(help_text=_("Detailed description of the expense."))
    reference_number = models.CharField(max_length=100, blank=True, null=True, help_text=_("e.g., Invoice # from vendor, Receipt #"))
    
    # How was this expense paid?
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.SET_NULL, # If method deleted, expense record remains
        null=True, blank=True, # Expense might be recorded "on account" (payable)
        help_text=_("How this expense was paid. Leave blank if recorded as a payable.")
    )
    # Alternatively, or in addition, link directly to the CoA asset/liability account
    # paid_from_account = models.ForeignKey(
    #     ChartOfAccount,
    #     on_delete=models.SET_NULL, null=True, blank=True,
    #     limit_choices_to=models.Q(account_type='ASSET') | models.Q(account_type='LIABILITY'), # Cash, Bank, or AP
    #     related_name='expenses_paid_from'
    # )

    recorded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, # Or 'schools.StaffUser'
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='expenses_recorded'
    )
    # attachment = models.FileField(upload_to='expense_attachments/', null=True, blank=True) # For receipts/invoices

    class Meta:
        verbose_name = _("Expense Record")
        verbose_name_plural = _("Expense Records")
        ordering = ['-expense_date', '-created_at']

    def __str__(self):
        return f"Expense: {self.category.name} - {self.amount} on {self.expense_date.strftime('%Y-%m-%d')}"


class Budget(models.Model):
    name = models.CharField(max_length=255, unique=True) # e.g., "2024-2025 Academic Year Budget"
    financial_year_start = models.DateField()
    financial_year_end = models.DateField()
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=False, help_text="Is this the currently active budget for reporting?")
    # created_at, updated_at
    
    class Meta:
        ordering = ['-financial_year_start', 'name']

    def __str__(self):
        return self.name



class BudgetItem(BaseModel):
    BUDGET_ITEM_TYPE_CHOICES = [
        ('INCOME', _('Income')),
        ('EXPENSE', _('Expense')),
    ]
    name = models.CharField(max_length=150, unique=True, help_text=_("Name of the budget line item."))
    description = models.TextField(blank=True, null=True)
    linked_coa_account = models.ForeignKey(
        'accounting.Account', # Use string 'app_label.ModelName' to avoid circular imports if defined later
        on_delete=models.PROTECT,
        related_name='budget_items',
        #  limit_choices_to=models.Q(account_type='INCOME') | models.Q(account_type='EXPENSE') | models.Q(account_type='COGS'),
        help_text=_("The Income, COGS or Expense account in CoA this budget item relates to.")
    )
    budget_item_type = models.CharField(max_length=10, choices=[('INCOME', _('Income')), ('EXPENSE', _('Expense'))]) # Simplified choices

    class Meta:
        verbose_name = _("Budget Item Category")
        verbose_name_plural = _("Budget Item Categories")
        ordering = ['budget_item_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_budget_item_type_display()})"
    


class BudgetAmount(BaseModel):
    budget_item = models.ForeignKey(BudgetItem, on_delete=models.CASCADE, related_name='amounts')
    academic_year = models.ForeignKey('schools.AcademicYear', on_delete=models.PROTECT, related_name='budget_amounts') # Assuming AcademicYear is in 'schools' app
    term = models.ForeignKey('schools.Term', on_delete=models.PROTECT, related_name='budget_amounts', blank=True, null=True) # Assuming Term is in 'schools' app
    budgeted_amount = models.DecimalField(max_digits=12, decimal_places=2)
    notes = models.TextField(blank=True, null=True)
    # Optional: You might also link this to a parent 'Budget' model if you have multiple versions or types of budgets
    # budget = models.ForeignKey('Budget', on_delete=models.CASCADE, related_name='budget_entries', null=True, blank=True)


    class Meta:
        verbose_name = _("Budgeted Amount")
        verbose_name_plural = _("Budgeted Amounts")
        ordering = ['academic_year', 'term', 'budget_item']
        # Consider unique constraints, e.g., unique_together = ('budget_item', 'academic_year', 'term')
        # if you only want one amount per item per period.
        unique_together = [('budget_item', 'academic_year', 'term')]


    def __str__(self):
        period = f"{self.academic_year.name}"
        if self.term:
            period += f" - {self.term.name}"
        return f"{self.budget_item.name}: {self.budgeted_amount} for {period}"



class FinancePermissions(models.Model):
    class Meta:
        managed = False
        default_permissions = ()
        permissions = [
            ('view_finance_module', _('Can view the main Finance module and navbar link')),
            ('manage_budgets', _('Can create and manage budgets')),
            ('manage_expenses', _('Can record and approve expenses')),
        ]
        
