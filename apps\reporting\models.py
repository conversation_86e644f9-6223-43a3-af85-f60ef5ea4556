# D:\school_fees_saas_V2\apps\reporting\models.py

from django.db import models
from django.utils.translation import gettext_lazy as _

class ReportingPermissions(models.Model):
    """
    A proxy model (managed = False) that doesn't create a database table.
    Its sole purpose is to define custom permissions for the 'reporting' app
    that can be assigned to Django Groups (and thus to users).
    These permissions will typically be used to control access to view-based reports.
    """
    class Meta:
        managed = False  # Django will not create a table for this model.
        default_permissions = () # No default 'add', 'change', 'delete', 'view' for this proxy.
        
        # Define custom permissions. 
        # Django will automatically prefix these with 'reporting.' when checking.
        # e.g., user.has_perm('reporting.view_collection_report')
        permissions = [
            # Basic Operational Reports
            ("view_collection_report", _("Can view Collection Report")),
            ("view_outstanding_fees_report", _("Can view Outstanding Fees Report")),
            ("view_student_ledger_report", _("Can view Student Ledger Report")),
            ("view_payment_summary_report", _("Can view Payment Summary Report")),

            # Financial Statements (Advanced Reports)
            ("view_trial_balance_report", _("Can view Trial Balance Report")),
            ("view_income_statement_report", _("Can view Income Statement (P&L)")),
            ("view_income_expense_report", _("Can view Income & Expense Report")),  # For IncomeExpenseReportView
            ("view_balance_sheet_report", _("Can view Balance Sheet Report")),
            ("view_cash_flow_statement_report", _("Can view Cash Flow Statement Report")),
            ("view_cash_flow_statement", _("Can view Cash Flow Statement")),  # For CashFlowStatementView

            # Budgeting Reports (Advanced Reports)
            ("view_budget_variance_report", _("Can view Budget Variance Report")),

            # Expense Reports (Advanced Reports)
            ("view_expense_report", _("Can view Expense Report")),

            # Fee/Projection Reports (Advanced Reports)
            ("view_fee_projection_report", _("Can view Fee Projection Report")),

            # Dashboard/General Reports
            ("view_report_dashboard", _("Can view the main reports dashboard page")),

            # Export permissions (for future use)
            # ("export_collection_report", _("Can export Collection Report")),
        ]
        verbose_name = _("Reporting Permission Set") 
        verbose_name_plural = _("Reporting Permission Sets")

    def __str__(self):
        # This string representation is mostly for Django admin or debugging,
        # as no instances of this proxy model will be created.
        return _("Custom Permissions for Reporting Module")




# # D:\school_fees_saas_v2\apps\reporting\models.py
# from django.db import models
# from django.utils.translation import gettext_lazy as _

# class ReportPermissions(models.Model):
#     """
#     A proxy model to hold all custom, app-level permissions for the reporting app.
#     This model does not create a database table.
#     """
#     class Meta:
#         managed = False  # No database table will be created for this model.
#         default_permissions = () # We define all permissions manually.
        
#         # Group permissions by type for clarity in the Django admin.
#         permissions = [
#             # --- Dashboard / General ---
#             ('view_report_dashboard', _('Can view the main reports dashboard page')),

#             # --- Fee and Student Related Reports ---
#             ('view_outstanding_fees_report', _('Can view outstanding fees report')),
#             ('view_collection_report', _('Can view fee collection report')),
#             ('view_payment_summary_report', _('Can view payment summary report')),
#             ('view_student_ledger_report', _('Can view student ledger report')),
#             ('view_fee_projection_report', _('Can view fee projection report')),
            
#             # --- Financial & Accounting Reports ---
#             ('view_expense_report', _('Can view expense report')),
#             ('view_trial_balance_report', _('Can view trial balance report')),
#             ('view_income_statement_report', _('Can view income statement (Profit & Loss)')),
#             ('view_balance_sheet_report', _('Can view balance sheet report')),
#             ('view_cash_flow_statement_report', _('Can view cash flow statement report')),
            
#             # --- Budgeting Reports ---
#             ('view_budget_variance_report', _('Can view budget variance report')),
#         ]
#         verbose_name = _("Report Permission")
#         verbose_name_plural = _("Report Permissions")