# Generated by Django 5.1.9 on 2025-06-24 21:40

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0005_seed_account_types'),
        ('payments', '0001_initial'),
        ('schools', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('financial_year_start', models.DateField()),
                ('financial_year_end', models.DateField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Is this the currently active budget for reporting?')),
            ],
            options={
                'ordering': ['-financial_year_start', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='Name of the supplier or vendor.', max_length=150, unique=True)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=30, null=True)),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True)),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state_province', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Vendor/Supplier',
                'verbose_name_plural': 'Vendors/Suppliers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BudgetItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='Name of the budget line item.', max_length=150, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('budget_item_type', models.CharField(choices=[('INCOME', 'Income'), ('EXPENSE', 'Expense')], max_length=10)),
                ('linked_coa_account', models.ForeignKey(help_text='The Income, COGS or Expense account in CoA this budget item relates to.', on_delete=django.db.models.deletion.PROTECT, related_name='budget_items', to='accounting.account')),
            ],
            options={
                'verbose_name': 'Budget Item Category',
                'verbose_name_plural': 'Budget Item Categories',
                'ordering': ['budget_item_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='e.g., Office Supplies, Utilities, Salaries', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('expense_account', models.ForeignKey(help_text='The Expense account in CoA this category posts to.', limit_choices_to={'account_type': 'EXPENSE'}, on_delete=django.db.models.deletion.PROTECT, related_name='expense_categories', to='accounting.account')),
            ],
            options={
                'verbose_name': 'Expense Category',
                'verbose_name_plural': 'Expense Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('expense_date', models.DateField(default=django.utils.timezone.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('description', models.TextField(help_text='Detailed description of the expense.')),
                ('reference_number', models.CharField(blank=True, help_text='e.g., Invoice # from vendor, Receipt #', max_length=100, null=True)),
                ('payment_method', models.ForeignKey(blank=True, help_text='How this expense was paid. Leave blank if recorded as a payable.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.paymentmethod')),
                ('recorded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses_recorded', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='expenses', to='finance.expensecategory')),
                ('vendor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses', to='finance.vendor')),
            ],
            options={
                'verbose_name': 'Expense Record',
                'verbose_name_plural': 'Expense Records',
                'ordering': ['-expense_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BudgetAmount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('budgeted_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='budget_amounts', to='schools.academicyear')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='budget_amounts', to='schools.term')),
                ('budget_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='amounts', to='finance.budgetitem')),
            ],
            options={
                'verbose_name': 'Budgeted Amount',
                'verbose_name_plural': 'Budgeted Amounts',
                'ordering': ['academic_year', 'term', 'budget_item'],
                'unique_together': {('budget_item', 'academic_year', 'term')},
            },
        ),
    ]
