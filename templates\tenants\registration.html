{# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}

{% load static i18n widget_tweaks tenant_extras %}

{% block title %}{% trans "Register Your School" %}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .registration-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    .registration-card .card-header {
        border-top-left-radius: 1rem;
        border-top-right-radius: 1rem;
        padding: 2rem 2rem 1.5rem;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
    }
    .form-floating > .form-control,
    .form-floating > .form-select {
        border-radius: 0.5rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    .form-floating > label {
        color: #6c757d;
        font-weight: 500;
    }
    .field-icon {
        margin-right: 0.5rem;
        color: #28a745;
    }
    .fieldset-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }
    .btn-primary {
        border-radius: 0.5rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(40, 167, 69, 0.3);
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    }
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .security-badge {
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.2);
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }
    @media (max-width: 768px) {
        .registration-card .card-header {
            padding: 1.5rem 1rem 1rem;
        }
        .school-icon {
            font-size: 2.5rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card registration-card">
                <div class="card-header text-white text-center">
                    <div class="school-icon mb-3">
                        <i class="bi bi-building" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 class="mb-2 fw-bold">{% trans "Register Your School" %}</h2>
                    <p class="mb-0 opacity-75">{% trans "Start Your Free Trial Today!" %}</p>
                </div>
                <div class="card-body p-4 p-md-5">
                    <div class="security-badge">
                        <i class="bi bi-shield-check text-success me-2" style="font-size: 1.5rem;"></i>
                        <strong>{% trans "Secure Registration" %}</strong><br>
                        <small class="text-muted">{% trans "Your data is protected with enterprise-grade security" %}</small>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">{{ form.non_field_errors|striptags }}</div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        <div class="fieldset-header mb-4">
                            <h5><i class="bi bi-building me-2"></i>{% trans "School Information" %}</h5>
                        </div>

                        <div class="mb-4">
                            <div class="form-floating">
                                {% render_field form.school_name class+="form-control" placeholder="e.g., Bright Future Academy" %}
                                <label for="{{ form.school_name.id_for_label }}">
                                    <i class="bi bi-building field-icon"></i>{{ form.school_name.label }}
                                </label>
                            </div>
                            {{ form.school_name.errors }}
                        </div>

                        <div class="mb-4">
                            <div class="form-floating">
                                {% render_field form.schema_name class+="form-control" placeholder="e.g., brightfuture" %}
                                <label for="{{ form.schema_name.id_for_label }}">
                                    <i class="bi bi-link-45deg field-icon"></i>{{ form.schema_name.label }}
                                </label>
                            </div>
                            <div class="help-text">
                                <i class="bi bi-info-circle me-1"></i>
                                {% trans "Your portal address will be: " %}
                                <strong>{{ form.schema_name.value|default_if_none:"yourprefix" }}.{% get_base_domain %}</strong>
                            </div>
                            {{ form.schema_name.errors }}
                        </div>

                        <div class="fieldset-header mb-4">
                            <h5><i class="bi bi-person-badge me-2"></i>{% trans "Your Administrator Account" %}</h5>
                        </div>

                        <div class="row g-4 mb-5">
                            <div class="col-md-6 mb-4">
                                <div class="form-floating">
                                    {% render_field form.owner_first_name class+="form-control" placeholder="First Name" %}
                                    <label for="{{ form.owner_first_name.id_for_label }}">
                                        <i class="bi bi-person field-icon"></i>{{ form.owner_first_name.label }}
                                    </label>
                                </div>
                                {{ form.owner_first_name.errors }}
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="form-floating">
                                    {% render_field form.owner_last_name class+="form-control" placeholder="Last Name" %}
                                    <label for="{{ form.owner_last_name.id_for_label }}">
                                        <i class="bi bi-person field-icon"></i>{{ form.owner_last_name.label }}
                                    </label>
                                </div>
                                {{ form.owner_last_name.errors }}
                            </div>
                            <div class="col-12 mb-4">
                                <div class="form-floating">
                                    {% render_field form.owner_email class+="form-control" placeholder="<EMAIL>" %}
                                    <label for="{{ form.owner_email.id_for_label }}">
                                        <i class="bi bi-envelope field-icon"></i>{{ form.owner_email.label }}
                                    </label>
                                </div>
                                {{ form.owner_email.errors }}
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="form-floating">
                                    {% render_field form.owner_password1 class+="form-control" placeholder="Password" %}
                                    <label for="{{ form.owner_password1.id_for_label }}">
                                        <i class="bi bi-lock field-icon"></i>{{ form.owner_password1.label }}
                                    </label>
                                </div>
                                {{ form.owner_password1.errors }}
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="form-floating">
                                    {% render_field form.owner_password2 class+="form-control" placeholder="Confirm Password" %}
                                    <label for="{{ form.owner_password2.id_for_label }}">
                                        <i class="bi bi-lock-fill field-icon"></i>{{ form.owner_password2.label }}
                                    </label>
                                </div>
                                {{ form.owner_password2.errors }}
                            </div>
                        </div>

                        <div class="fieldset-header mb-4">
                            <h5><i class="bi bi-credit-card me-2"></i>{% trans "Select Your Plan" %}</h5>
                        </div>

                        <div class="mb-5">
                            {{ form.plan.errors }}
                            {% for radio in form.plan %}
                            <div class="form-check mb-3 plan-option border rounded p-3"
                                data-price-monthly="{{ radio.choice_value.instance.price_monthly|default_if_none:'0.00' }}"
                                data-price-annually="{{ radio.choice_value.instance.price_annually|default_if_none:'0.00' }}"
                                data-trial-days="{{ radio.choice_value.instance.trial_period_days|default_if_none:'0' }}"
                                style="transition: all 0.3s ease; cursor: pointer;">
                                {{ radio.tag }}
                                <label for="{{ radio.id_for_label }}" class="form-check-label fw-bold" style="cursor: pointer;">
                                        <strong>{{ radio.choice_label }}</strong>
                                        {% with plan_instance=radio.choice_value.instance %}
                                            <small class="d-block text-muted">
                                                {% if plan_instance.trial_period_days > 0 %}
                                                    {% blocktrans with days=plan_instance.trial_period_days %}Includes a {{ days }}-day free trial.{% endblocktrans %}
                                                {% endif %}
                                                {{ plan_instance.description|truncatewords:20 }}
                                            </small>
                                            <div class="plan-pricing mt-1">
                                                {# Prices will be updated by JS #}
                                            </div>
                                            <ul class="list-unstyled list-inline list-inline-dotted small mt-1">
                                                {% for feature_item in plan_instance.features.all|slice:":3" %}
                                                    <li class="list-inline-item"><i class="bi bi-check-circle-fill text-success me-1"></i>{{ feature_item.name }}</li>
                                                {% empty %}
                                                    <li class="list-inline-item">{% trans "Core Features" %}</li>
                                                {% endfor %}
                                                {% if plan_instance.features.all.count > 3 %}<li class="list-inline-item">& {% trans "more" %}...</li>{% endif %}
                                            </ul>
                                        {% endwith %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                <label class="form-label d-block mb-2">{% trans "Billing Cycle" %}:</label>
                                {{ form.billing_cycle.errors }}
                                {% for radio_cycle in form.billing_cycle %}
                                <div class="form-check form-check-inline">
                                    {{ radio_cycle.tag }}
                                    <label for="{{ radio_cycle.id_for_label }}" class="form-check-label">{{ radio_cycle.choice_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                            <div id="selected-plan-price-display" class="alert alert-info" style="display:none;">
                                {% trans "Selected Plan Price" %}: <strong id="plan-price"></strong>
                            </div>
                        </fieldset>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check2-circle me-2"></i> {% trans "Complete Registration" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function () {
    const planRadios = document.querySelectorAll('input[name="plan"]');
    const cycleRadios = document.querySelectorAll('input[name="billing_cycle"]');
    const priceDisplayDiv = document.getElementById('selected-plan-price-display');
    const priceStrong = document.getElementById('plan-price');
    const currencySymbol = "{{ school_currency_symbol|default:'$' }}"; // Get from context if available globally, or hardcode

    function updatePriceDisplay() {
        let selectedPlanRadio = document.querySelector('input[name="plan"]:checked');
        let selectedCycleRadio = document.querySelector('input[name="billing_cycle"]:checked');

        if (selectedPlanRadio && selectedCycleRadio) {
            const planOptionDiv = selectedPlanRadio.closest('.plan-option');
            const priceMonthly = parseFloat(planOptionDiv.dataset.priceMonthly);
            const priceAnnually = parseFloat(planOptionDiv.dataset.priceAnnually);
            const trialDays = parseInt(planOptionDiv.dataset.trialDays);
            const cycle = selectedCycleRadio.value;
            
            let priceText = "";
            if (trialDays > 0) {
                priceText += `{% blocktrans with days=999 %}First ${trialDays} days FREE, then {% endblocktrans %}`.replace('999', trialDays);
            }

            if (cycle === 'MONTHLY') {
                priceText += `${currencySymbol}${priceMonthly.toFixed(2)} {% trans "/ month" %}`;
            } else if (cycle === 'ANNUALLY') {
                priceText += `${currencySymbol}${priceAnnually.toFixed(2)} {% trans "/ year" %}`;
            }
            
            priceStrong.textContent = priceText;
            priceDisplayDiv.style.display = 'block';

        } else {
            priceDisplayDiv.style.display = 'none';
        }
    }

    planRadios.forEach(radio => radio.addEventListener('change', updatePriceDisplay));
    cycleRadios.forEach(radio => radio.addEventListener('change', updatePriceDisplay));

    // Initial update in case of pre-selection or errors
    updatePriceDisplay();
});
</script>
{% endblock %}





























{% comment %} {# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title|default:"Register Your School" }}{% endblock %}

{% block page_specific_css %}
    {# If you have specific styles for public forms, link them here #}
    {# Example: <link rel="stylesheet" href="{% static 'css/public_form_styles.css' %}"> #}
    {# Using tenant_form_styles for consistency for now #}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title|default:"Register Your School" }}</h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        <p class="text-muted small mb-3">Fields marked with <span class="text-danger">*</span> are required.</p>

                        {# Display form non-field errors (e.g., from form.clean()) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error|escape }}<br>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <fieldset class="mb-4">
                            <legend class="h5 mb-3 border-bottom pb-2">School Information</legend>
                            {# School Name #}
                            <div class="mb-3">
                                {{ form.school_name.label_tag }}
                                {{ form.school_name }} {# Widget should have form-control class #}
                                {% if form.school_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.school_name.help_text }}</small>{% endif %}
                                {% if form.school_name.errors %}<div class="invalid-feedback d-block">{{ form.school_name.errors|striptags }}</div>{% endif %}
                            </div>

                            {# Subdomain with Input Group #}
                            <div class="mb-3">
                                {{ form.subdomain.label_tag }}
                                <div class="input-group">
                                    {{ form.subdomain }} {# Input field - widget should have form-control #}
                                    {# Append the base domain extension #}
                                    {% if tenant_base_hostname_for_display %}
                                        <span class="input-group-text">.{{ tenant_base_hostname_for_display }}</span>
                                    {% endif %}
                                </div>
                                {# Help text from the form will appear below #}
                                {% if form.subdomain.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.subdomain.help_text }}</small>{% endif %}
                                {% if form.subdomain.errors %}<div class="invalid-feedback d-block">{{ form.subdomain.errors|striptags }}</div>{% endif %}
                            </div>
                        </fieldset>

                        <fieldset class="mt-4">
                            <legend class="h5 mb-3 border-bottom pb-2">Administrator Account Details</legend>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_first_name.label_tag }}
                                    {{ form.admin_first_name }}
                                    {% if form.admin_first_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_first_name.help_text }}</small>{% endif %}
                                    {% if form.admin_first_name.errors %}<div class="invalid-feedback d-block">{{ form.admin_first_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_last_name.label_tag }}
                                    {{ form.admin_last_name }}
                                    {% if form.admin_last_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_last_name.help_text }}</small>{% endif %}
                                    {% if form.admin_last_name.errors %}<div class="invalid-feedback d-block">{{ form.admin_last_name.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                {{ form.admin_email.label_tag }}
                                {{ form.admin_email }}
                                {% if form.admin_email.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_email.help_text }}</small>{% endif %}
                                {% if form.admin_email.errors %}<div class="invalid-feedback d-block">{{ form.admin_email.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_password1.label_tag }}
                                    {{ form.admin_password1 }}
                                    {% if form.admin_password1.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_password1.help_text }}</small>{% endif %}
                                    {% if form.admin_password1.errors %}<div class="invalid-feedback d-block">{{ form.admin_password1.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_password2.label_tag }}
                                    {{ form.admin_password2 }}
                                    {% if form.admin_password2.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_password2.help_text }}</small>{% endif %}
                                    {% if form.admin_password2.errors %}<div class="invalid-feedback d-block">{{ form.admin_password2.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <button type="submit" class="btn btn-success w-100 btn-lg mt-4">Register School</button>
                    </form>
                    <div class="text-center mt-3">
                        <p>Already registered a school? <a href="{% url 'users:school_admin_login' %}">Administrator Login</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
 {% endcomment %}

