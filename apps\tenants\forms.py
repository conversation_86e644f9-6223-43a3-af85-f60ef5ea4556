# D:\school_fees_saas_v2\apps\tenants\forms.py
from django import forms
from django.contrib.auth import get_user_model
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.conf import settings # To get TENANT_BASE_DOMAIN for help text
import re # For subdomain validation

# Import models needed for choices or validation
from apps.subscriptions.models import SubscriptionPlan # For plan selection
# from apps.tenants.models import School # Only if validating unique schema_name here

User = get_user_model() # This will be your public User model (settings.AUTH_USER_MODEL)

class RegistrationForm(forms.Form):
    # --- School Information ---
    school_name = forms.CharField(
        max_length=100, 
        required=True, 
        label=_("School Name*"),
        widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("e.g., Bright Future Academy")})
    )
    subdomain = forms.SlugField( # Using SlugField which inherently validates for allowed characters
        max_length=63, # Max length for a DNS label
        required=True,
        label=_("Desired Portal Address Prefix*"), 
        help_text=_("Use lowercase letters, numbers, and hyphens only. This cannot be changed later."), 
        widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("e.g., brightfuture")})
    )

    # --- Administrator Account Details ---
    admin_first_name = forms.CharField(
        max_length=150, 
        required=True, 
        label=_("Your First Name*"),
        widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("John")})
    )
    admin_last_name = forms.CharField(
        max_length=150, 
        required=True, 
        label=_("Your Last Name*"),
        widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("Doe")})
    )
    admin_email = forms.EmailField(
        required=True, 
        label=_("Your Email Address* (This will be your login)"),
        widget=forms.EmailInput(attrs={'class': 'form-control mb-3', 'placeholder': _("<EMAIL>"), 'autocomplete': 'email'}) # Changed to 'email' for better autofill
    )
    admin_password1 = forms.CharField(
        label=_("Create Password*"),
        widget=forms.PasswordInput(attrs={'class': 'form-control mb-3', 'autocomplete': 'new-password'}),
        required=True, 
        min_length=8 # Good practice
    )
    admin_password2 = forms.CharField(
        label=_("Confirm Password*"),
        widget=forms.PasswordInput(attrs={'class': 'form-control mb-3', 'autocomplete': 'new-password'}),
        required=True
    )

    # --- Subscription Plan Selection ---
    plan = forms.ModelChoiceField(
        queryset=SubscriptionPlan.objects.filter(is_active=True, is_public=True).order_by('display_order', 'price_monthly'),
        label=_("Choose Your Plan*"),
        empty_label=None, # Force selection
        widget=forms.RadioSelect, # Radio buttons are often good for a few plan choices
        required=True,
        help_text=_("Select the plan that best suits your school's needs.")
    )
    billing_cycle = forms.ChoiceField(
        choices=SubscriptionPlan.BILLING_CYCLE_CHOICES, # From SubscriptionPlan model
        label=_("Preferred Billing Cycle*"),
        widget=forms.RadioSelect,
        initial='MONTHLY', # Default selection
        required=True
    )
    
    # --- Terms and Conditions ---
    agree_to_terms = forms.BooleanField(
        required=True,
        label=_("I agree to the <a href=\"#\" target=\"_blank\">Terms of Service</a> and <a href=\"#\" target=\"_blank\">Privacy Policy</a>.*"),
        # Replace # with actual URLs to your terms and privacy pages
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )


    def __init__(self, *args, **kwargs):
        # <<< FIX IS HERE >>>
        # 1. POP the custom argument. We'll use it later.
        base_domain_for_help_text = kwargs.pop('base_domain_for_help_text', None)

        # 2. Now call the parent initializer. kwargs is now clean.
        super().__init__(*args, **kwargs)

        # 3. Use the value you popped (or fallback to settings).
        if not base_domain_for_help_text:
            base_domain_from_settings = getattr(settings, 'TENANT_BASE_DOMAIN', 'myapp.test:8000')
            base_domain_for_help_text = base_domain_from_settings.split(':')[0]

        self.fields['subdomain'].help_text = _(
            "Your school portal will be accessible at [your-prefix].%(hostname)s. "
            "Use only lowercase letters, numbers, and hyphens. This cannot be changed later."
        ) % {'hostname': base_domain_for_help_text}

        # --- The rest of your __init__ logic is fine ---
        try:
            terms_url = reverse_lazy('public_site:terms_of_service')
            privacy_url = reverse_lazy('public_site:privacy_policy')
            self.fields['agree_to_terms'].label = _(
                "I have read and agree to the <a href=\"%(terms_url)s\" target=\"_blank\">Terms of Service</a> "
                "and <a href=\"%(privacy_url)s\" target=\"_blank\">Privacy Policy</a>.*"
            ) % {'terms_url': terms_url, 'privacy_url': privacy_url}
        except Exception:
            self.fields['agree_to_terms'].label = _(
                "I agree to the Terms of Service and Privacy Policy.*"
            )


    def clean_admin_email(self):
        email = self.cleaned_data.get('admin_email')
        if email and User.objects.filter(email__iexact=email).exists():
            # This checks against the public User model (settings.AUTH_USER_MODEL)
            raise forms.ValidationError(_("An account with this email address already exists on our platform. Please use a different email or try logging in."))
        return email


    def clean_subdomain(self):
        subdomain = self.cleaned_data.get('subdomain', '').lower()
        
        # SlugField already validates characters, but we can add more specific checks
        if not subdomain: # Should be caught by required=True, but defensive
            raise forms.ValidationError(_("Portal address prefix cannot be empty."))
        
        # Check against reserved words (moved to a constant for better maintainability)
        RESERVED_SUBDOMAINS = [
            'www', 'admin', 'mail', 'ftp', 'blog', 'shop', 'support', 'dev', 'staging', 
            'app', 'portal', 'api', 'static', 'media', 'user', 'users', 'account', 
            'accounts', 'root', 'info', 'test', 'payment', 'billing', 'secure', 
            'internal', 'system', 'public', 'assets', 'files', 'cdn', 'status',
            'cpanel', 'webmail', 'smtp', 'pop', 'imap', 'news', 'store', 'api-docs',
            settings.TENANT_BASE_DOMAIN.split('.')[0] if '.' in settings.TENANT_BASE_DOMAIN else '' # e.g., 'myapp' from 'myapp.test'
        ] 
        # Filter out empty string from reserved if base domain doesn't have a dot
        RESERVED_SUBDOMAINS = [r for r in RESERVED_SUBDOMAINS if r]

        if subdomain in RESERVED_SUBDOMAINS:
            raise forms.ValidationError(_("This portal address prefix ('%(subdomain)s') is reserved or too common. Please choose a more unique one.") % {'subdomain': subdomain})
        
        if len(subdomain) < 3:
            raise forms.ValidationError(_("Portal address prefix must be at least 3 characters long."))

        # Check for existing schema_name (Tenant model)
        # It's good to do this here for immediate feedback, 
        # though the view will also catch it with an IntegrityError.
        from apps.tenants.models import School # Import here to avoid circularity at module level
        if School.objects.filter(schema_name__iexact=subdomain).exists():
            raise forms.ValidationError(_("This portal address prefix is already in use. Please choose another."))
            
        return subdomain


    def clean(self):
        cleaned_data = super().clean()
        password_1 = cleaned_data.get("admin_password1")
        password_2 = cleaned_data.get("admin_password2")

        if password_1 and password_2 and password_1 != password_2:
            self.add_error('admin_password2', _("The two password fields didn't match."))
        
        # Ensure agree_to_terms is checked
        if not cleaned_data.get('agree_to_terms'):
            self.add_error('agree_to_terms', _("You must agree to the terms and conditions to proceed."))
            
        return cleaned_data
