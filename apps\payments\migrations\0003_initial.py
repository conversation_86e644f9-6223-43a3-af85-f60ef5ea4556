# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0002_initial'),
        ('fees', '0003_initial'),
        ('payments', '0002_initial'),
        ('schools', '0001_initial'),
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='parent_payer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments_made', to='students.parentuser'),
        ),
        migrations.AddField(
            model_name='payment',
            name='processed_by_staff',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payments', to='schools.staffuser'),
        ),
        migrations.AddField(
            model_name='payment',
            name='student',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments_for_student', to='students.student'),
        ),
        migrations.AddField(
            model_name='paymentallocation',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='allocations', to='fees.invoice'),
        ),
        migrations.AddField(
            model_name='paymentallocation',
            name='payment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='payments.payment'),
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='linked_account',
            field=models.ForeignKey(blank=True, help_text='The Cash, Bank, or Payment Gateway Clearing account in CoA.', limit_choices_to={'account_type__classification': 'ASSET', 'account_type__normal_balance': 'DR'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payment_methods_linked', to='accounting.account'),
        ),
        migrations.AddField(
            model_name='payment',
            name='payment_method',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payments_using_this_method', to='payments.paymentmethod'),
        ),
        migrations.AlterUniqueTogether(
            name='paymentallocation',
            unique_together={('payment', 'invoice')},
        ),
    ]
