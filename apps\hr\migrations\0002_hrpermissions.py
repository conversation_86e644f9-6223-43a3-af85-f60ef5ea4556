# Generated by Django 5.1.9 on 2025-07-05 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='HRPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_hr_module', 'Can view the main HR module and navbar link'), ('manage_staff_users', 'Can create, edit, and manage staff accounts'), ('approve_leave_requests', 'Can approve or reject leave requests'), ('manage_leave_types', 'Can configure leave types')],
                'managed': False,
                'default_permissions': (),
            },
        ),
    ]
