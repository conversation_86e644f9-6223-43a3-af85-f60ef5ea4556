# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = False

    dependencies = [
        ('accounting', '0002_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('schools', '0001_initial'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='academicsetting',
            name='current_academic_year',
            field=models.ForeignKey(blank=True, help_text='The currently active academic year for this school.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='schools.academicyear'),
        ),
        migrations.AlterUniqueTogether(
            name='schoolclass',
            unique_together={('name',)},
        ),
        migrations.CreateModel(
            name='SchoolProfile',
            fields=[
                ('school', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='schoolprofile', serialize=False, to='tenants.school')),
                ('school_name_override', models.CharField(blank=True, help_text='Optional. If you want a display name different from the registered tenant name.', max_length=255, null=True, verbose_name='School Name Override')),
                ('school_motto', models.CharField(blank=True, max_length=255, null=True, verbose_name='School Motto')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='school_logos/', verbose_name='School Logo')),
                ('school_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='School Email')),
                ('phone_number', models.CharField(blank=True, max_length=30, null=True, verbose_name='School Phone')),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True, verbose_name='Address Line 1')),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True, verbose_name='Address Line 2')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='City')),
                ('state_province', models.CharField(blank=True, max_length=100, null=True, verbose_name='State/Province')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='Postal Code')),
                ('country_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Country Name')),
                ('financial_year_start_month', models.PositiveSmallIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], default=1, help_text="The starting month of the school's financial year.", verbose_name='Financial Year Start Month')),
                ('currency_symbol', models.CharField(blank=True, default='$', max_length=5, null=True, verbose_name='Currency Symbol')),
                ('school_name_on_reports', models.CharField(blank=True, help_text='Name to display on official reports/invoices.', max_length=255, null=True, verbose_name='School Name on Reports')),
                ('default_due_days', models.PositiveIntegerField(default=15, help_text='Default number of days invoices are due after issue date.', verbose_name='Default Invoice Due Days')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_academic_year', models.ForeignKey(blank=True, help_text="The current active academic year for this school's operations.", null=True, on_delete=django.db.models.deletion.SET_NULL, to='schools.academicyear', verbose_name='Current Academic Year')),
                ('default_accounts_receivable_coa', models.ForeignKey(blank=True, help_text='Default Accounts Receivable account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_ar_default', to='accounting.account', verbose_name='Default Accounts Receivable Account')),
                ('default_bank_coa', models.ForeignKey(blank=True, help_text='Default primary Bank account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_bank_default', to='accounting.account', verbose_name='Default Bank Account')),
                ('default_cash_coa', models.ForeignKey(blank=True, help_text='Default Cash on Hand account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_cash_default', to='accounting.account', verbose_name='Default Cash Account')),
                ('default_discount_given_coa', models.ForeignKey(blank=True, help_text='Default Discount Given (expense/contra-income) account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_discount_default', to='accounting.account', verbose_name='Default Discount Given Account')),
                ('default_expense_coa', models.ForeignKey(blank=True, help_text='Default General Expense account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_expense_default', to='accounting.account', verbose_name='Default General Expense Account')),
                ('default_fee_income_coa', models.ForeignKey(blank=True, help_text='Default Fee Income account from CoA.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_fee_income_default', to='accounting.account', verbose_name='Default Fee Income Account')),
            ],
            options={
                'verbose_name': 'School Profile',
                'verbose_name_plural': 'School Profile Settings',
                'permissions': [('view_outstanding_fees_report', 'Can view outstanding fees report'), ('view_fee_collection_report', 'Can view fee collection report'), ('view_expense_report', 'Can view expense report'), ('view_revenue_report', 'Can view revenue report'), ('manage_school_profile', 'Can manage school profile settings'), ('manage_academic_settings', 'Can manage academic year and term settings')],
            },
        ),
        migrations.CreateModel(
            name='StaffUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('email', models.EmailField(help_text='Used for login and communication.', max_length=254, unique=True, verbose_name='email address')),
                ('first_name', models.CharField(max_length=150, verbose_name='first name')),
                ('middle_name', models.CharField(blank=True, max_length=100, verbose_name='middle name')),
                ('last_name', models.CharField(max_length=150, verbose_name='last name')),
                ('employee_id', models.CharField(blank=True, help_text='School-specific unique employee ID.', max_length=50, null=True, unique=True, verbose_name='employee ID')),
                ('designation', models.CharField(blank=True, help_text='Job title or role.', max_length=100, verbose_name='designation')),
                ('department', models.CharField(blank=True, help_text='e.g., Academics, Administration, Finance.', max_length=100, verbose_name='department')),
                ('employment_type', models.CharField(blank=True, choices=[('', '---------'), ('FullTime', 'Full-Time'), ('PartTime', 'Part-Time'), ('Contract', 'Contract'), ('Permanent', 'Permanent'), ('Intern', 'Intern'), ('Volunteer', 'Volunteer')], max_length=50, null=True, verbose_name='employment type')),
                ('date_hired', models.DateField(blank=True, help_text='Date when employment started.', null=True, verbose_name='date hired')),
                ('date_left', models.DateField(blank=True, help_text='Date when employment ended, if applicable.', null=True, verbose_name='date left')),
                ('gender', models.CharField(blank=True, choices=[('', '---------'), ('Male', 'Male'), ('Female', 'Female'), ('Other', 'Other'), ('Prefer_not_to_say', 'Prefer not to say')], max_length=20, verbose_name='gender')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='date of birth')),
                ('marital_status', models.CharField(blank=True, choices=[('', '---------'), ('Single', 'Single'), ('Married', 'Married'), ('Divorced', 'Divorced'), ('Widowed', 'Widowed'), ('Other', 'Other')], max_length=20, verbose_name='marital status')),
                ('phone_number_primary', models.CharField(blank=True, max_length=30, verbose_name='primary phone')),
                ('phone_number_alternate', models.CharField(blank=True, max_length=30, verbose_name='alternate phone')),
                ('address_line1', models.CharField(blank=True, max_length=255, verbose_name='address line 1')),
                ('address_line2', models.CharField(blank=True, help_text='Apartment, suite, unit, building, floor, etc.', max_length=255, verbose_name='address line 2')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='city')),
                ('state_province', models.CharField(blank=True, max_length=100, verbose_name='state/province')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='postal/zip code')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='country')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='staff_photos/', verbose_name='profile photo')),
                ('notes', models.TextField(blank=True, help_text='Internal notes about the staff member.', verbose_name='internal notes')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user account is active. Unselect this instead of deleting accounts.', verbose_name='active status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into the Django admin site. Should typically be False for regular staff.', verbose_name='Django admin access')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined system')),
                ('is_owner_profile', models.BooleanField(default=False, help_text='Designates if this staff profile is the automatically created one for the tenant owner, linking their public account to tenant super admin rights.', verbose_name='is owner profile')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this staff user belongs to. A user will get all permissions granted to each of their groups.', related_name='staffuser_set', related_query_name='staffuser', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this staff user.', related_name='staffuser_set', related_query_name='staffuser', to='auth.permission', verbose_name='staff user permissions')),
            ],
            options={
                'verbose_name': 'staff member',
                'verbose_name_plural': 'staff members',
                'ordering': ['last_name', 'first_name', 'email'],
            },
        ),
        migrations.CreateModel(
            name='Section',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='e.g., A, B, Blue Group', max_length=50)),
                ('room_number', models.CharField(blank=True, help_text='Optional: Room number or name where this section meets.', max_length=50, verbose_name='Room Number/Name')),
                ('school_class', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='schools.schoolclass', verbose_name='Class/Grade')),
                ('class_teacher', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='class_sections_taught', to='schools.staffuser', verbose_name='Class Teacher')),
            ],
            options={
                'verbose_name': 'Section',
                'verbose_name_plural': 'Sections',
                'ordering': ['school_class__name', 'name'],
                'unique_together': {('name', 'school_class')},
            },
        ),
        migrations.CreateModel(
            name='Term',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='e.g., Term 1, First Semester, Quarter 3', max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=False, help_text='Is this term currently active for operations?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='terms', to='schools.academicyear')),
            ],
            options={
                'verbose_name': 'Term / Semester',
                'verbose_name_plural': 'Terms / Semesters',
                'ordering': ['academic_year__start_date', 'start_date', 'name'],
                'unique_together': {('academic_year', 'name')},
            },
        ),
    ]
