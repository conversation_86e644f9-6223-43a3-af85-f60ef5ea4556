# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeProfile',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='hr_profile', serialize=False, to='schools.staffuser')),
                ('middle_name', models.CharField(blank=True, max_length=100, verbose_name='middle name(s)')),
                ('gender', models.CharField(blank=True, choices=[('MALE', 'Male'), ('FEMALE', 'Female'), ('OTHER', 'Other'), ('UNSPECIFIED', 'Prefer not to say')], max_length=15, null=True, verbose_name='gender')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='date of birth')),
                ('marital_status', models.CharField(blank=True, choices=[('SINGLE', 'Single'), ('MARRIED', 'Married'), ('DIVORCED', 'Divorced'), ('WIDOWED', 'Widowed'), ('OTHER', 'Other')], max_length=15, null=True, verbose_name='marital status')),
                ('phone_number_alternate', models.CharField(blank=True, help_text='Optional secondary contact number.', max_length=30, verbose_name='alternate phone')),
                ('address_line1', models.CharField(blank=True, max_length=255, verbose_name='address line 1')),
                ('address_line2', models.CharField(blank=True, max_length=255, verbose_name='address line 2')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='city')),
                ('state_province', models.CharField(blank=True, max_length=100, verbose_name='state/province')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='postal/zip code')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='country')),
                ('employment_type', models.CharField(blank=True, choices=[('FULL_TIME', 'Full-Time'), ('PART_TIME', 'Part-Time'), ('CONTRACT', 'Contract'), ('INTERN', 'Intern'), ('TEMPORARY', 'Temporary')], max_length=20, null=True, verbose_name='employment type')),
                ('date_left', models.DateField(blank=True, null=True, verbose_name='date left employment')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='staff_photos/', verbose_name='profile photo')),
                ('notes', models.TextField(blank=True, verbose_name='internal notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'employee HR profile',
                'verbose_name_plural': 'employee HR profiles',
                'ordering': ['user__last_name', 'user__first_name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the leave type (e.g., Annual Leave, Sick Leave, Maternity Leave).', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_annual_days', models.DecimalField(blank=True, decimal_places=1, default=Decimal('0.0'), help_text='Maximum days that can be typically accrued or granted per year for this leave type. Use 0 or None for no limit/manual assignment.', max_digits=5, null=True, verbose_name='max annual days')),
                ('is_paid', models.BooleanField(default=True, help_text='Is this leave type paid?')),
                ('requires_approval', models.BooleanField(default=True, help_text='Does this leave type require manager/admin approval?')),
                ('is_active', models.BooleanField(default=True, help_text='Is this leave type currently available for new requests and active in the system?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'leave type',
                'verbose_name_plural': 'leave types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='start date')),
                ('end_date', models.DateField(verbose_name='end date')),
                ('half_day_start', models.BooleanField(default=False, verbose_name='Half Day (Start)')),
                ('half_day_end', models.BooleanField(default=False, verbose_name='Half Day (End)')),
                ('reason', models.TextField(verbose_name='reason for leave')),
                ('attachment', models.FileField(blank=True, help_text='Optional: Attach any supporting document (e.g., medical certificate).', null=True, upload_to='leave_attachments/%Y/%m/', verbose_name='attachment')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED_BY_STAFF', 'Cancelled by Staff')], default='PENDING', max_length=20, verbose_name='status')),
                ('request_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='request submitted on')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='approval date')),
                ('admin_notes', models.TextField(blank=True, null=True, verbose_name='admin/manager notes')),
                ('number_of_days_requested_calc', models.DecimalField(decimal_places=1, default=Decimal('0.0'), help_text='Calculated number of leave days by the system.', max_digits=5, verbose_name='calculated number of days')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_requests', to='hr.employeeprofile')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.employeeprofile')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='leave_requests', to='hr.leavetype')),
            ],
            options={
                'verbose_name': 'leave request',
                'verbose_name_plural': 'leave requests',
                'ordering': ['-request_date', 'employee'],
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_or_period_info', models.CharField(blank=True, help_text="E.g., '2024', '2024-2025 Academic Year', 'Q1 2025'", max_length=50, null=True)),
                ('days_accrued', models.DecimalField(decimal_places=1, default=Decimal('0.0'), max_digits=5, verbose_name='days accrued/entitled')),
                ('days_taken', models.DecimalField(decimal_places=1, default=Decimal('0.0'), max_digits=5, verbose_name='days taken')),
                ('last_accrual_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='hr.employeeprofile')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='hr.leavetype')),
            ],
            options={
                'verbose_name': 'leave balance',
                'verbose_name_plural': 'leave balances',
                'ordering': ['employee__user__last_name', 'leave_type__name'],
                'unique_together': {('employee', 'leave_type', 'year_or_period_info')},
            },
        ),
    ]
