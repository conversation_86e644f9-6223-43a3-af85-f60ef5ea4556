# D:\school_fees_saas_v2\apps\subscriptions\models.py

from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from apps.tenants.models import School # Import School for ForeignKey

# --- Feature Model (Defines available features/add-ons) ---
class Feature(models.Model):
    code = models.CharField(max_length=50, unique=True, help_text=_("Short unique code for the feature (e.g., HR_MODULE, PARENT_PORTAL)."))
    name = models.CharField(max_length=150, help_text=_("User-friendly name of the feature."))
    description = models.TextField(blank=True, help_text=_("Optional description."))
    
    class Meta:
        ordering = ['name']
        verbose_name = _("Plan Feature")
        verbose_name_plural = _("Plan Features")

    def __str__(self):
        return self.name

# --- Subscription Plan Model (Defines the tiers) ---
class SubscriptionPlan(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, unique=True, blank=True, help_text=_("URL-friendly version of the name, auto-generated if blank."))
    description = models.TextField(blank=True)
    
    price_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text=_("Price per month if billed monthly."))
    price_annually = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text=_("Price per year if billed annually."))
    
    # Payment Gateway Price IDs
    pg_price_id_monthly = models.CharField(max_length=100, blank=True, null=True, help_text=_("Price ID from payment gateway for monthly billing."))
    pg_price_id_annually = models.CharField(max_length=100, blank=True, null=True, help_text=_("Price ID from payment gateway for annual billing."))
    
    # --- CORE OF THIS PRICING MODEL ---
    max_students = models.PositiveIntegerField(
        default=50, 
        help_text=_("The maximum number of active students allowed for this plan.")
    )
    max_staff = models.PositiveIntegerField(
        null=True, blank=True, 
        help_text=_("Maximum number of active staff users allowed. Leave blank for unlimited.")
    )
    
    # Kept for optional major feature add-ons
    features = models.ManyToManyField(Feature, blank=True, related_name='plans')
    
    is_active = models.BooleanField(default=True, help_text=_("Is this plan currently offered for new subscriptions?"))
    is_public = models.BooleanField(default=True, help_text=_("Is this plan visible on the public pricing page?"))
    display_order = models.PositiveIntegerField(default=0, help_text=_("Order for display on pricing page (0=first)."))
    
    trial_period_days = models.PositiveIntegerField(default=0, help_text=_("Number of trial days for this plan (0 for no trial)."))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['display_order', 'price_monthly']
        verbose_name = _("Subscription Plan")
        verbose_name_plural = _("Subscription Plans")
    
    def __str__(self):
        return f"{self.name} (Up to {self.max_students} students)"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

# --- Subscription Model (Links a School to a Plan) ---
class Subscription(models.Model):
    class Status(models.TextChoices):
        PENDING = 'PENDING', _('Pending Setup')
        TRIALING = 'TRIALING', _('Trialing')
        ACTIVE = 'ACTIVE', _('Active')
        PAST_DUE = 'PAST_DUE', _('Past Due')
        CANCELLED = 'CANCELLED', _('Cancelled')
        ENDED = 'ENDED', _('Ended')
        # Add other statuses as needed from your payment gateway (e.g., INCOMPLETE)
    
    class BillingCycle(models.TextChoices):
        MONTHLY = 'MONTHLY', _('Monthly')
        ANNUALLY = 'ANNUALLY', _('Annually')

    school = models.OneToOneField(
        School, 
        on_delete=models.CASCADE,
        related_name='subscription' 
    )
    plan = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.PROTECT,
        related_name='subscriptions'
    )
    status = models.CharField(max_length=25, choices=Status.choices, default=Status.PENDING, db_index=True)
    billing_cycle = models.CharField(max_length=10, choices=BillingCycle.choices, default=BillingCycle.MONTHLY)
    
    # Payment Gateway Info
    pg_subscription_id = models.CharField(max_length=100, blank=True, null=True, unique=True, db_index=True)
    pg_customer_id = models.CharField(max_length=100, blank=True, null=True, db_index=True)
    
    # Dates
    trial_end_date = models.DateTimeField(null=True, blank=True)
    current_period_start = models.DateTimeField(null=True, blank=True)
    current_period_end = models.DateTimeField(null=True, blank=True, db_index=True)
    
    # Cancellation Info
    cancel_at_period_end = models.BooleanField(default=False)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    notes = models.TextField(blank=True, help_text=_("Internal notes about this subscription."))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['school__name']
        verbose_name = _("School Subscription")
        verbose_name_plural = _("School Subscriptions")

    def __str__(self):
        return f"Subscription for {self.school.name} ({self.plan.name}) - Status: {self.get_status_display()}"

    @property
    def is_usable(self):
        """Determines if the tenant should have access to the app's features."""
        return self.status in [self.Status.ACTIVE, self.Status.TRIALING]

    @property
    def is_on_trial(self):
        return self.status == self.Status.TRIALING and self.trial_end_date and self.trial_end_date >= timezone.now()

    def has_feature(self, feature_code):
        """Checks if this subscription's plan includes a specific optional feature."""
        if not self.is_usable or not self.plan:
            return False
        return self.plan.features.filter(code__iexact=feature_code).exists()
    
    
    



# # D:\school_fees_saas_V2\apps\subscriptions\models.py
# from django.db import models
# from django.utils import timezone
# from decimal import Decimal
# from apps.tenants.models import School # Import School for ForeignKey

# # --- Feature Model (Should already be here) ---
# class Feature(models.Model):
#     code = models.CharField(max_length=50, unique=True, help_text="Short unique code for the feature (e.g., HR_MODULE, PARENT_PORTAL).")
#     name = models.CharField(max_length=150, help_text="User-friendly name of the feature.")
#     description = models.TextField(blank=True, help_text="Optional description.")
    
#     # is_core_feature = models.BooleanField(default=False, help_text="Is this a core feature available in all/most plans?") # Optional
    
#     class Meta:
#         ordering = ['name']
#         verbose_name = "Plan Feature"
#         verbose_name_plural = "Plan Features"
#     def __str__(self):
#         return self.name

# # --- Subscription Plan Model (Should already be here) ---
# class SubscriptionPlan(models.Model):
#     BILLING_CYCLE_CHOICES = [('MONTHLY', 'Monthly'), ('ANNUALLY', 'Annually')]
    
#     name = models.CharField(max_length=100, unique=True)
#     slug = models.SlugField(max_length=120, unique=True, blank=True, help_text="URL-friendly version of the name, auto-generated if blank.") # NEW: For URLs
#     description = models.TextField(blank=True)
    
#     price_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text="Price per month if billed monthly.")
#     price_annually = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text="Price per year if billed annually (often discounted).")
    
#     # Payment Gateway Price IDs (essential for real integration)
#     pg_price_id_monthly = models.CharField(max_length=100, blank=True, null=True, help_text="Price ID from payment gateway for monthly billing.")
#     pg_price_id_annually = models.CharField(max_length=100, blank=True, null=True, help_text="Price ID from payment gateway for annual billing.")
    
#     # Usage Limits
#     max_students = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum number of active students allowed. Null for unlimited.")
#     max_staff = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum number of active staff users allowed. Null for unlimited.")
#     # max_storage_gb = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum storage in GB. Null for unlimited.") # Example
    
#     features = models.ManyToManyField(Feature, blank=True, related_name='plans_offering_feature') # Changed related_name slightly
    
#     is_active = models.BooleanField(default=True, help_text="Is this plan currently offered for new subscriptions?")
#     is_public = models.BooleanField(default=True, help_text="Is this plan visible on the public pricing page?")
#     display_order = models.PositiveIntegerField(default=0, help_text="Order in which plans are displayed on pricing page.") # NEW: For ordering
    
#     trial_period_days = models.PositiveIntegerField(default=0, help_text="Number of trial days offered for this plan (0 for no trial).") # NEW

#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
    
#     class Meta:
#         ordering = ['display_order', 'price_monthly', 'name']
#         verbose_name = "Subscription Plan"
#         verbose_name_plural = "Subscription Plans"
    
#     def __str__(self):
#         return self.name

#     def save(self, *args, **kwargs):
#         if not self.slug:
#             from django.utils.text import slugify
#             self.slug = slugify(self.name)
#         super().save(*args, **kwargs)

# #     BILLING_CYCLE_CHOICES = [('MONTHLY', 'Monthly'), ('ANNUALLY', 'Annually')]
# #     name = models.CharField(max_length=100, unique=True)
# #     description = models.TextField(blank=True)
# #     price_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
# #     price_annually = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
# #     pg_price_id_monthly = models.CharField(max_length=100, blank=True, null=True)
# #     pg_price_id_annually = models.CharField(max_length=100, blank=True, null=True)
# #     max_students = models.PositiveIntegerField(null=True, blank=True)
# #     max_staff = models.PositiveIntegerField(null=True, blank=True)
# #     features = models.ManyToManyField(Feature, blank=True, related_name='plans')
# #     is_active = models.BooleanField(default=True)
# #     is_public = models.BooleanField(default=True)
# #     created_at = models.DateTimeField(auto_now_add=True)
# #     updated_at = models.DateTimeField(auto_now=True)
# #     class Meta:
# #         ordering = ['price_monthly', 'name']
# #         verbose_name = "Subscription Plan"
# #         verbose_name_plural = "Subscription Plans"
# #     def __str__(self):
# #         return self.name

# # --- Subscription Model (Ensure this is defined) ---
# # apps/subscriptions/models.py
# from apps.tenants.models import School # Ensure this is your public tenant model

# class Subscription(models.Model):
#     STATUS_CHOICES = [
#         ('PENDING', 'Pending Payment/Setup'), # NEW: For initial state before payment/trial
#         ('TRIALING', 'Trialing'), 
#         ('ACTIVE', 'Active'), 
#         ('PAST_DUE', 'Past Due'), # Payment failed
#         ('CANCELLED', 'Cancelled'), # User explicitly cancelled
#         ('UNPAID', 'Unpaid'), # Similar to PAST_DUE, maybe after multiple failures
#         ('INCOMPLETE', 'Incomplete'), # Stripe concept, for setup issues
#         ('INCOMPLETE_EXPIRED', 'Incomplete Expired'), # Stripe concept
#         ('ENDED', 'Ended'), # Trial ended, or fixed term ended without renewal
#         ('SUSPENDED', 'Suspended'), # Manually suspended by admin
#     ]
#     BILLING_CYCLE_CHOICES = SubscriptionPlan.BILLING_CYCLE_CHOICES

#     school = models.OneToOneField(
#         School, 
#         on_delete=models.CASCADE, # If school is deleted, subscription is gone
#         related_name='subscription' 
#     )
#     plan = models.ForeignKey(
#         SubscriptionPlan,
#         on_delete=models.PROTECT, # Don't delete a plan if subscriptions are linked
#         related_name='subscriptions_to_plan' # Changed related_name
#     )
#     status = models.CharField(max_length=25, choices=STATUS_CHOICES, default='PENDING', db_index=True) # Default to PENDING
#     billing_cycle = models.CharField(max_length=10, choices=BILLING_CYCLE_CHOICES, default='MONTHLY') # This might be redundant if plan implies it, or useful if a subscription can override plan default for some reason.
    
#     # Payment Gateway Subscription Info
#     pg_subscription_id = models.CharField(max_length=100, blank=True, null=True, unique=True, db_index=True, help_text="Subscription ID from the payment gateway.")
#     pg_customer_id = models.CharField(max_length=100, blank=True, null=True, db_index=True, help_text="Customer ID from the payment gateway.")
    
#     # Dates
#     trial_start_date = models.DateTimeField(null=True, blank=True) # NEW
#     trial_end_date = models.DateTimeField(null=True, blank=True)
#     current_period_start = models.DateTimeField(null=True, blank=True, help_text="Start of the current paid billing period.")
#     current_period_end = models.DateTimeField(null=True, blank=True, db_index=True, help_text="End of current paid billing period (or trial). Access might be restricted after this if status isn't Active/Trialing.")
    
#     # Cancellation Info
#     cancel_at_period_end = models.BooleanField(default=False, help_text="If true, subscription will be cancelled at period end, not renewed.")
#     cancelled_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when cancellation was processed.")
#     ended_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when subscription truly ended (e.g., after trial, or after cancelled period end).") # NEW

#     # Price at time of subscription (Snapshot) - Important if plan prices change
#     price_at_subscription = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Actual price this school subscribed at for the current cycle.") # NEW

#     notes = models.TextField(blank=True, help_text="Internal notes about this subscription.") # NEW

#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['school__name']
#         verbose_name = "School Subscription"
#         verbose_name_plural = "School Subscriptions"

#     def __str__(self):
#         school_name = self.school.name if self.school else "Unknown School"
#         plan_name = self.plan.name if self.plan else "Unknown Plan"
#         return f"Subscription for {school_name} ({plan_name}) - Status: {self.get_status_display()}"

#     @property
#     def is_usable(self):
#         """Determines if the tenant should have access to features based on subscription status."""
#         return self.status in ['ACTIVE', 'TRIALING']

#     @property
#     def is_on_trial(self):
#         return self.status == 'TRIALING' and self.trial_end_date and self.trial_end_date >= timezone.now()

#     def has_feature(self, feature_code):
#         """Checks if this subscription's plan includes a specific feature."""
#         if not self.is_usable or not self.plan:
#             return False
#         return self.plan.features.filter(code__iexact=feature_code).exists()
        
#     # Other methods: start_trial(), activate(), cancel(), handle_payment_failure(), etc.

