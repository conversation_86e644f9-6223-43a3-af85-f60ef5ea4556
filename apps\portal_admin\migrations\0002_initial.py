# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('portal_admin', '0001_initial'),
        ('schools', '0002_initial'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='adminactivitylog',
            name='staff_user',
            field=models.ForeignKey(blank=True, help_text='The tenant staff user who performed the action.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='staff_activity_logs', to='schools.staffuser'),
        ),
        migrations.AddField(
            model_name='adminactivitylog',
            name='target_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype', verbose_name='target model type'),
        ),
        migrations.AddField(
            model_name='adminactivitylog',
            name='tenant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='tenants.school', verbose_name='Associated School/Tenant'),
        ),
    ]
