# D:\school_fees_saas_V2\apps\common\context_processors.py
import logging
from django.conf import settings
from django_tenants.utils import get_public_schema_name, get_tenant_model

# Assuming your models are correctly located
from apps.subscriptions.models import Feature 
# --- Import Models Safely ---
# It's better to handle potential ImportErrors gracefully or ensure apps are loaded.
# For context processors, which run on every request, being robust is key.

User = None
School = None # Your Tenant Model
StaffUser = None
ParentUser = None
EmployeeProfile = None # From HR
Subscription = None
Feature = None # From Subscriptions
GlobalAcademicYearFilterForm = None # From common.forms
AcademicYear = None # From schools.models

try:
    from django.contrib.auth import get_user_model
    User = get_user_model() # This is your PublicUserModel
    PublicUserModel = User # Alias for clarity in existing code
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error: Could not import PublicUserModel (AUTH_USER_MODEL): {e}")

try:
    School = get_tenant_model()
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error: Could not get TenantModel (School): {e}")

try:
    from apps.schools.models import StaffUser as SUModel, AcademicYear as AYModel
    StaffUser = SUModel
    AcademicYear = AYModel
except ImportError as e:
    logging.getLogger(__name__).warning(f"CP Warning: Could not import StaffUser or AcademicYear: {e}")
except Exception as e: # Catch any other app registry issues
    logging.getLogger(__name__).error(f"CP Error importing from schools.models: {e}")


try:
    from apps.students.models import ParentUser as PUModel
    # If Student model is needed here, import it too
    ParentUser = PUModel
except ImportError as e:
    logging.getLogger(__name__).warning(f"CP Warning: Could not import ParentUser: {e}")
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error importing from students.models: {e}")

try:
    from apps.hr.models import EmployeeProfile as EPModel
    EmployeeProfile = EPModel
except ImportError as e:
    logging.getLogger(__name__).warning(f"CP Warning: Could not import EmployeeProfile: {e}")
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error importing from hr.models: {e}")


try:
    from apps.subscriptions.models import Subscription as SubModel, Feature as FeatModel
    Subscription = SubModel
    Feature = FeatModel
except ImportError as e:
    logging.getLogger(__name__).warning(f"CP Warning: Could not import Subscription or Feature: {e}")
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error importing from subscriptions.models: {e}")

try:
    from apps.common.forms import GlobalAcademicYearFilterForm as GAYFForm
    GlobalAcademicYearFilterForm = GAYFForm
except ImportError as e:
    logging.getLogger(__name__).warning(f"CP Warning: Could not import GlobalAcademicYearFilterForm: {e}")
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error importing from common.forms: {e}")


logger = logging.getLogger(__name__)

# --- Default Settings (Consider moving to a constants.py or settings) ---
DEFAULT_ACTIVE_FEATURES = {
    'FEE_MANAGEMENT': True, 
    'STUDENT_MANAGEMENT': True, 
    'ACCOUNTING': True, 
    'REPORTS_BASIC': True, 
    'HR_MODULE': False, 
    'HR_LEAVE': False, 
    'EXPENSE_TRACKING': False, 
    'BUDGETING': False, 
    'REPORTS_ADVANCED': False,
    'PARENT_PORTAL': False, 
    'ONLINE_PAYMENTS': False, 
    'EMAIL_NOTIFICATIONS': False,
    # Add any new feature codes here with their default state (usually False for optional)
}

DEFAULT_PLAN_LIMITS = {
    'MAX_STUDENTS': None, 'MAX_STAFF': None,
}

NAVBAR_FLAG_KEYS = [
    'show_dashboard_link', 'show_manage_students_link', 'show_fees_management_dropdown', 
    'show_hr_staff_dropdown', 'show_finance_dropdown', 'show_reports_dropdown', 
    'show_setup_admin_dropdown', 'show_invoices_link', 'show_concessions_link', 
    'show_fee_allocations_link', 'show_manage_staff_users_link', 'show_leave_management_link', 
    'show_hr_staff_separator', 'show_leave_type_management_link', 'show_staff_leave_request_link',
    'show_expenses_link', 'show_budgets_link', 'show_chart_of_accounts_link',
    'show_manual_journal_entries_link', 'show_announcements_link',
    'show_collection_report_link', 'show_outstanding_fees_link', 
    'show_student_ledger_report_link', 'show_payment_summary_report_link', 
    'show_expense_report_link', 'show_budget_variance_report_link', 
    'show_trial_balance_link', 'show_income_statement_link', 'show_balance_sheet_link', 
    'show_cash_flow_link', 'show_fee_projection_report_link', 'show_school_profile_link', 
    'show_manage_classes_link', 'show_manage_academic_years_link', 'show_manage_terms_link',
    'show_manage_other_academic_settings_link', 'show_manage_coa_link',
    'show_manage_fee_heads_link', 'show_manage_fee_structures_link', 
    'show_manage_payment_methods_link', 'show_manage_roles_link', 
    'show_assign_staff_to_roles_link', 'show_activity_log_link', 
    'show_parent_dashboard_link', 'show_parent_children_fees_link', 
    'show_parent_profile_link', 'show_parent_online_payment_link', 
]



def tenant_features_processor(request):
    """
    Adds a dictionary 'tenant_features' to the context for tenant requests.
    Keys are feature codes, values are True/False indicating if the tenant's
    current active subscription plan includes that feature.
    Also adds 'active_subscription_details' for convenience.
    """
    context = {
        'tenant_features': {}, # Default to empty dict (no features)
        'active_subscription_details': None,
    }

    # Check if we are in a tenant context (not public schema)
    if hasattr(request, 'tenant') and request.tenant and \
        request.tenant.schema_name != get_public_schema_name():
        
        try:
            # Access the subscription through the related name from the School (tenant) model
            # This assumes your School model has:
            # subscription = OneToOneField(Subscription, related_name='school_subscription_details', ...)
            # OR the related_name is 'subscription' on the Subscription model's OneToOneField to School.
            # Your Subscription model has: school = models.OneToOneField(School, related_name='subscription')
            # So, request.tenant.subscription should work.
            
            active_subscription = getattr(request.tenant, 'subscription', None)

            if active_subscription and active_subscription.is_usable:
                context['active_subscription_details'] = active_subscription # Pass the whole object
                
                # Get all globally defined feature codes
                # Cache this query if it becomes a performance concern (e.g., using Django's cache framework)
                all_defined_feature_codes = list(Feature.objects.values_list('code', flat=True))
                
                tenant_feature_flags = {}
                for code in all_defined_feature_codes:
                    # Use the has_feature method on the Subscription model instance
                    tenant_feature_flags[code] = active_subscription.has_feature(code)
                
                context['tenant_features'] = tenant_feature_flags
                logger.debug(f"Tenant '{request.tenant.schema_name}': Features loaded: {tenant_feature_flags}")
            else:
                logger.debug(f"Tenant '{request.tenant.schema_name}': No usable active subscription found or plan missing features.")
                # Keep tenant_features as empty dict if no usable subscription
        
        except AttributeError: # In case request.tenant doesn't have .subscription (e.g., setup issue)
            logger.error(f"Tenant '{request.tenant.schema_name}': Missing 'subscription' attribute. Ensure OneToOneField is set up correctly.")
        except Exception as e:
            logger.error(f"Error in tenant_features_processor for tenant '{request.tenant.schema_name}': {e}", exc_info=True)
            
    return context



# --- Main Consolidated Context Processor ---
def global_tenant_context(request):
    # Use the already imported models, checking if they loaded successfully
    user = getattr(request, 'user', None)
    current_tenant_obj = getattr(request, 'tenant', None)
    
    # effective_tenant_user should be set by middleware if owner is acting as staff
    user_for_tenant_perms = getattr(request, 'effective_tenant_user', user)

    # Initialize context dictionaries
    user_type_flags = {key: False for key in ['IS_PUBLIC_ADMIN_USER', 'IS_TENANT_STAFF_USER', 
                                            'IS_TENANT_PARENT_USER', 'IS_AUTHENTICATED_USER', 
                                            'USER_HAS_HR_PROFILE']}
    active_features = DEFAULT_ACTIVE_FEATURES.copy()
    plan_limits = DEFAULT_PLAN_LIMITS.copy()
    owned_school_for_public_admin = None # For public navbar, school owner's school
    navbar_flags = {key: False for key in NAVBAR_FLAG_KEYS}
    
    is_public_schema_active = (not current_tenant_obj or 
                            (School and current_tenant_obj.schema_name == get_public_schema_name()))

    # --- 1. Populate user_type_flags ---
    if user and user.is_authenticated:
        user_type_flags['IS_AUTHENTICATED_USER'] = True
        
        # Check if StaffUser model was loaded and if user_for_tenant_perms is an instance
        if StaffUser and isinstance(user_for_tenant_perms, StaffUser):
            user_type_flags['IS_TENANT_STAFF_USER'] = True
            if EmployeeProfile and hasattr(user_for_tenant_perms, 'hr_profile'): # 'hr_profile' is the related_name
                try:
                    if user_for_tenant_perms.hr_profile is not None: # Accessing the OneToOneField
                        user_type_flags['USER_HAS_HR_PROFILE'] = True
                        logger.debug(f"CP: User {user_for_tenant_perms.email} HAS hr_profile.")
                except EmployeeProfile.DoesNotExist: # Catch if profile doesn't exist
                    logger.debug(f"CP: User {user_for_tenant_perms.email} does NOT have hr_profile (DoesNotExist).")
                except Exception as e: 
                    logger.error(f"CP: Error checking hr_profile for {user_for_tenant_perms.email}: {e}", exc_info=settings.DEBUG)
        
        # Check if ParentUser model was loaded and if request.user is an instance
        if ParentUser and isinstance(user, ParentUser):
            user_type_flags['IS_TENANT_PARENT_USER'] = True
        
        # Check if PublicUserModel was loaded and if request.user is an instance
        if PublicUserModel and isinstance(user, PublicUserModel):
            if user.is_superuser or (hasattr(user, 'is_staff') and user.is_staff): 
                user_type_flags['IS_PUBLIC_ADMIN_USER'] = True # Platform admin
            
            if School and current_tenant_obj and \
                hasattr(current_tenant_obj, 'owner_id') and current_tenant_obj.owner_id == user.pk:
                user_type_flags['IS_PUBLIC_ADMIN_USER'] = True # Also flag owner of current tenant
            
            # For public navbar: display link to first owned school
            if School and hasattr(user, 'owned_schools'):
                try:
                    # This query only hits the 'public' schema's tenant table.
                    # This does not load the school's profile from the tenant schema.
                    owned_school_for_public_admin = user.owned_schools.filter(is_active=True).order_by('created_on').first()
                except Exception as e:
                    logger.error(f"CP: Error fetching owned_schools for {user.email}: {e}", exc_info=settings.DEBUG)

    # --- 2. Determine Active Tenant Features & Limits ---
    if not is_public_schema_active and current_tenant_obj and Subscription and Feature:
        # This part only runs if we are on a tenant schema
        try:
            # tenant.subscription should be efficient if prefetched or cached
            tenant_subscription = getattr(current_tenant_obj, 'subscription', None) 
            if tenant_subscription and tenant_subscription.is_usable and tenant_subscription.plan:
                plan = tenant_subscription.plan
                if hasattr(plan, 'max_students'): plan_limits['MAX_STUDENTS'] = plan.max_students
                if hasattr(plan, 'max_staff'): plan_limits['MAX_STAFF'] = plan.max_staff
                
                plan_feature_codes = set(plan.features.filter.values_list('code', flat=True)) # Only active features
                
                # Start with default features, then override based on plan
                current_plan_active_features = DEFAULT_ACTIVE_FEATURES.copy()
                for code in current_plan_active_features.keys(): # Iterate over known default feature codes
                    if code in plan_feature_codes:
                        current_plan_active_features[code] = True
                    else:
                        # If it's a default 'True' feature but not in plan, does the plan override it to False?
                        # This depends on your business logic for DEFAULT_ACTIVE_FEATURES.
                        # Simpler: plan explicitly enables. If not in plan, it's off unless it's a core forced feature.
                        current_plan_active_features[code] = False # Assume plan must grant it
                
                # Ensure core features are always enabled for simplicity, overriding plan if necessary
                # These are features essential for the app to function for any paying customer perhaps.
                CORE_FORCED_FEATURES = ['STUDENT_MANAGEMENT', 'FEE_MANAGEMENT', 'ACCOUNTING', 'REPORTS_BASIC']
                for core_feat in CORE_FORCED_FEATURES:
                    if core_feat in current_plan_active_features:
                        current_plan_active_features[core_feat] = True
                
                active_features = current_plan_active_features
            else:
                logger.warning(f"CP: Tenant '{current_tenant_obj.name}' has no usable subscription/plan. Using default features.")
                # active_features remains DEFAULT_ACTIVE_FEATURES
        except AttributeError: # e.g., if current_tenant_obj has no 'subscription'
            logger.warning(f"CP: Tenant '{current_tenant_obj.name}' missing 'subscription' attribute. Using default features.")
        except Exception as e:
            logger.error(f"CP: Error processing subscription for tenant '{current_tenant_obj.name}': {e}", exc_info=settings.DEBUG)
            # Fallback to defaults if subscription processing fails

    # --- 3. Set Navbar Flags ---
    # user_for_tenant_perms is the key for tenant-specific permissions
    # It should be a StaffUser instance if owner logged into tenant portal, or a regular StaffUser.
    
    is_tenant_context_for_perms = (not is_public_schema_active and current_tenant_obj and \
                                    user_for_tenant_perms and user_for_tenant_perms.is_authenticated)

    if is_tenant_context_for_perms and hasattr(user_for_tenant_perms, 'has_perm'):
        is_tenant_superuser = getattr(user_for_tenant_perms, 'is_superuser', False)
        
        if is_tenant_superuser:
            logger.debug(f"CP: User {user_for_tenant_perms.email} is a tenant superuser in '{current_tenant_obj.name}'. Enabling most staff navbar flags by default.")
            for key in NAVBAR_FLAG_KEYS:
                if not key.startswith('show_parent_'): # Don't show parent links to staff by default
                    navbar_flags[key] = True

            # Ensure HR dropdown is enabled for superusers (force override)
            navbar_flags['show_hr_staff_dropdown'] = True
            navbar_flags['show_manage_staff_users_link'] = True
            logger.debug("CP: Superuser - Force enabled HR dropdown and staff management")
            logger.debug(f"CP: Superuser - HR dropdown flag set to: {navbar_flags.get('show_hr_staff_dropdown', 'NOT_SET')}")
        else: # Regular staff user, check individual permissions
            logger.debug(f"CP: User {user_for_tenant_perms.email} is NOT a tenant superuser in '{current_tenant_obj.name}'. Checking individual perms.")
            # --- Staff Dashboard & Core Modules ---
            navbar_flags['show_dashboard_link'] = True # All authenticated staff see their dashboard
            if active_features.get('STUDENT_MANAGEMENT') and user_for_tenant_perms.has_perm('students.view_student'):
                navbar_flags['show_manage_students_link'] = True
            
            # --- Fees Management Dropdown ---
            if active_features.get('FEE_MANAGEMENT'):
                can_view_invoice = user_for_tenant_perms.has_perm('fees.view_invoice')
                can_view_concession = user_for_tenant_perms.has_perm('fees.view_concessiontype')
                navbar_flags['show_invoices_link'] = can_view_invoice
                navbar_flags['show_concessions_link'] = can_view_concession
                if can_view_invoice or can_view_concession:
                    navbar_flags['show_fees_management_dropdown'] = True



            # --- Finance Dropdown ---
            if active_features.get('ACCOUNTING'): # Base for finance items
                can_view_expense = active_features.get('EXPENSE_TRACKING') and user_for_tenant_perms.has_perm('finance.view_expense')
                can_view_budget = active_features.get('BUDGETING') and user_for_tenant_perms.has_perm('finance.view_budget')
                can_view_coa = user_for_tenant_perms.has_perm('accounting.view_account') # Using 'account' as the model
                can_view_je = user_for_tenant_perms.has_perm('accounting.view_journalentry')

                navbar_flags['show_expenses_link'] = can_view_expense
                navbar_flags['show_budgets_link'] = can_view_budget
                navbar_flags['show_chart_of_accounts_link'] = can_view_coa
                navbar_flags['show_manual_journal_entries_link'] = can_view_je

                if can_view_expense or can_view_budget or can_view_coa or can_view_je:
                    navbar_flags['show_finance_dropdown'] = True
            
            # --- Reports Dropdown ---
            if active_features.get('REPORTS_BASIC') or active_features.get('REPORTS_ADVANCED'):
                # Basic Reports
                if active_features.get('REPORTS_BASIC'):
                    navbar_flags['show_collection_report_link'] = user_for_tenant_perms.has_perm('reporting.view_collection_report')
                    navbar_flags['show_outstanding_fees_link'] = user_for_tenant_perms.has_perm('reporting.view_outstanding_fees_report')
                    navbar_flags['show_student_ledger_report_link'] = user_for_tenant_perms.has_perm('reporting.view_student_ledger_report')
                    navbar_flags['show_payment_summary_report_link'] = user_for_tenant_perms.has_perm('reporting.view_payment_summary_report')
                # Advanced Reports
                if active_features.get('REPORTS_ADVANCED'):
                    if active_features.get('EXPENSE_TRACKING'):
                        navbar_flags['show_expense_report_link'] = user_for_tenant_perms.has_perm('reporting.view_expense_report')
                    if active_features.get('BUDGETING'):
                        navbar_flags['show_budget_variance_report_link'] = user_for_tenant_perms.has_perm('reporting.view_budget_variance_report')
                    if active_features.get('ACCOUNTING'):
                        navbar_flags['show_trial_balance_link'] = user_for_tenant_perms.has_perm('reporting.view_trial_balance_report')
                        navbar_flags['show_income_statement_link'] = user_for_tenant_perms.has_perm('reporting.view_income_statement_report')
                        navbar_flags['show_balance_sheet_link'] = user_for_tenant_perms.has_perm('reporting.view_balance_sheet_report')
                        navbar_flags['show_cash_flow_link'] = user_for_tenant_perms.has_perm('reporting.view_cash_flow_statement_report')
                    navbar_flags['show_fee_projection_report_link'] = user_for_tenant_perms.has_perm('reporting.view_fee_projection_report')

                # If any report link is true, show the main dropdown
                if any(navbar_flags[k] for k in NAVBAR_FLAG_KEYS if 'report_link' in k or k in ['show_trial_balance_link', 'show_income_statement_link', 'show_balance_sheet_link', 'show_cash_flow_link']):
                    navbar_flags['show_reports_dropdown'] = True

            # --- Setup & Admin Dropdown ---
            if True: # Assume setup is always potentially visible, governed by perms
                navbar_flags['show_school_profile_link'] = user_for_tenant_perms.has_perm('schools.change_schoolprofile')
                navbar_flags['show_manage_classes_link'] = user_for_tenant_perms.has_perm('schools.view_schoolclass')
                navbar_flags['show_manage_academic_years_link'] = user_for_tenant_perms.has_perm('schools.view_academicyear')
                navbar_flags['show_manage_terms_link'] = user_for_tenant_perms.has_perm('schools.view_term')
                navbar_flags['show_manage_other_academic_settings_link'] = user_for_tenant_perms.has_perm('schools.change_academicsetting') # Example perm
                
                if active_features.get('ACCOUNTING') and not navbar_flags['show_chart_of_accounts_link']: # If not already in Finance dropdown
                    navbar_flags['show_manage_coa_link'] = user_for_tenant_perms.has_perm('accounting.view_account')
                
                if active_features.get('FEE_MANAGEMENT'):
                    navbar_flags['show_manage_fee_heads_link'] = user_for_tenant_perms.has_perm('fees.view_feehead')
                    navbar_flags['show_manage_fee_structures_link'] = user_for_tenant_perms.has_perm('fees.view_feestructure')
                
                navbar_flags['show_manage_payment_methods_link'] = user_for_tenant_perms.has_perm('payments.view_paymentmethod')
                navbar_flags['show_manage_roles_link'] = user_for_tenant_perms.has_perm('auth.view_group')
                navbar_flags['show_assign_staff_to_roles_link'] = user_for_tenant_perms.has_perm('portal_admin.assign_staff_roles') # Custom perm
                navbar_flags['show_activity_log_link'] = user_for_tenant_perms.has_perm('portal_admin.view_adminactivitylog') # Custom perm

                # --- Announcements Link ---
                # Note: For superusers, this flag is already set to True above (line 295)
                # But we need to ensure it respects the feature flag
                if not active_features.get('ANNOUNCEMENTS_SCHOOL', False):
                    navbar_flags['show_announcements_link'] = False
                    logger.debug("CP: ANNOUNCEMENTS_SCHOOL feature disabled, setting show_announcements_link = False")
                else:
                    logger.debug(f"CP: ANNOUNCEMENTS_SCHOOL feature enabled, keeping show_announcements_link = {navbar_flags.get('show_announcements_link', 'NOT_SET')}")

                # If any setup link is true, show the main dropdown
                setup_keys = ['show_school_profile_link', 'show_manage_classes_link', 'show_manage_academic_years_link',
                            'show_manage_terms_link', 'show_manage_other_academic_settings_link', 'show_manage_coa_link',
                            'show_manage_fee_heads_link', 'show_manage_fee_structures_link',
                            'show_manage_payment_methods_link', 'show_manage_roles_link',
                            'show_assign_staff_to_roles_link', 'show_activity_log_link', 'show_announcements_link']
                if any(navbar_flags[k] for k in setup_keys):
                    navbar_flags['show_setup_admin_dropdown'] = True
        
        # Selective override by tenant_features AFTER superuser's blanket allowance (if superuser logic was above)
        # This ensures that even a superuser doesn't see links to features the school hasn't subscribed to.
        if is_tenant_superuser and active_features: # <<< CORRECTED: Use active_features
            logger.debug(f"CP: Superuser override: Checking active_features: {active_features}")

            # Add similar overrides for other major feature modules based on active_features
            # For example, if 'FINANCE_ADVANCED' is a feature:
            # if not active_features.get('FINANCE_ADVANCED', True):
            #     navbar_flags['show_budgets_link'] = False 
            #     navbar_flags['show_budget_variance_report_link'] = False
            if not active_features.get('PARENT_PORTAL', True):
                parent_flags = [k for k in NAVBAR_FLAG_KEYS if k.startswith('show_parent_')]
                for flag in parent_flags:
                    if flag in navbar_flags: navbar_flags[flag] = False

    # Parent User Navbar Flags (this logic is largely fine if user_type_flags is correct)
    elif user_type_flags['IS_TENANT_PARENT_USER'] and ParentUser: # Check ParentUser model loaded
        if active_features.get('PARENT_PORTAL'):
            logger.debug(f"CP: Setting parent-specific navbar flags for {user_for_tenant_perms.email}.")
            navbar_flags['show_parent_dashboard_link'] = True
            navbar_flags['show_parent_children_fees_link'] = True 
            navbar_flags['show_parent_profile_link'] = True
            if active_features.get('ONLINE_PAYMENTS'):
                navbar_flags['show_parent_online_payment_link'] = True
            
            # Ensure all staff-specific flags are False for parents
            for key in NAVBAR_FLAG_KEYS:
                if not key.startswith('show_parent_'):
                    navbar_flags[key] = False
    else: 
        logger.debug("CP: User not authenticated in tenant or no 'perms_to_check_on' for setting navbar flags.")


    # --- 4. Global Academic Year Filter Form context ---
    global_academic_year_filter_form_instance = None
    selected_global_academic_year_obj = None 
    
    # Condition to show the filter: user is authenticated AND is tenant staff AND form/models are available AND on a tenant page
    if user_type_flags['IS_AUTHENTICATED_USER'] and user_type_flags['IS_TENANT_STAFF_USER'] and \
        GlobalAcademicYearFilterForm and AcademicYear and current_tenant_obj and \
        not is_public_schema_active:
        
        session_key = f"selected_global_ay_pk_{current_tenant_obj.schema_name}"
        current_selected_pk_str = request.session.get(session_key)
        current_selected_pk_int = None
        if current_selected_pk_str:
            try:
                current_selected_pk_int = int(current_selected_pk_str)
            except ValueError:
                logger.warning(f"CP: Invalid non-integer AY PK '{current_selected_pk_str}' in session for key '{session_key}'.")
                if session_key in request.session: del request.session[session_key]

        if AcademicYear: # Ensure model is loaded
            global_academic_year_filter_form_instance = GlobalAcademicYearFilterForm(
                tenant=current_tenant_obj, 
                initial={'academic_year': current_selected_pk_int}
            )
            if current_selected_pk_int:
                try:
                    selected_global_academic_year_obj = AcademicYear.objects.get(pk=current_selected_pk_int)
                except AcademicYear.DoesNotExist:
                    logger.warning(f"CP: Selected global AY PK {current_selected_pk_int} not found for tenant {current_tenant_obj.name}. Clearing from session.")
                    if session_key in request.session: del request.session[session_key]
                    selected_global_academic_year_obj = None
    
    # --- 5. Resolve current view/app info ---
    current_app_name = getattr(request.resolver_match, 'app_name', None)
    current_url_name = getattr(request.resolver_match, 'url_name', None)
    current_view_name = getattr(request.resolver_match, 'view_name', None)
    
    context_to_return = {
        'user_type_flags': user_type_flags,
        'tenant_features': active_features,
        'tenant_plan_limits': plan_limits,
        'navbar_flags': navbar_flags,
        'current_tenant': current_tenant_obj,
        'owned_school_for_public_admin': owned_school_for_public_admin,
        
        'current_app_name': current_app_name,
        'current_view_name': current_view_name, # Fully qualified view name
        'current_url_name': current_url_name,   # URL name without namespace

        'global_academic_year_filter_form': global_academic_year_filter_form_instance,
        'selected_global_academic_year_obj': selected_global_academic_year_obj, 
        
        'DJANGO_DEBUG': settings.DEBUG,
        'user_for_tenant_perms': user_for_tenant_perms, # Pass this for debug block in tenant_base
    }
    
    # --- Conditional Debug Logging ---
    if settings.DEBUG and current_tenant_obj and not is_public_schema_active and \
        (user_type_flags.get('IS_TENANT_STAFF_USER') or user_type_flags.get('IS_TENANT_PARENT_USER')):
        
        schema_name = getattr(current_tenant_obj, 'schema_name', 'N/A')
        user_email_for_log = getattr(user_for_tenant_perms, 'email', None) or getattr(user, 'email', 'UnknownUser')

        logger.debug(f"--- global_tenant_context DEBUG OUTPUT (Tenant: {schema_name}, User: {user_email_for_log}) ---")
        logger.debug(f"  request.user: {getattr(user, 'email', 'Anonymous')}, Type: {type(user)}")
        logger.debug(f"  user_for_tenant_perms: {getattr(user_for_tenant_perms, 'email', 'None')}, Type: {type(user_for_tenant_perms)}")
        logger.debug(f"  user_type_flags: {user_type_flags}")
        logger.debug(f"  active_features: {active_features}")
        logger.debug(f"  plan_limits: {plan_limits}")
        logger.debug(f"  navbar_flags (sorted): {dict(sorted(navbar_flags.items()))}")
        logger.debug(f"  Current App/View/URLName: {current_app_name}/{current_view_name}/{current_url_name}")
        logger.debug(f"  Global AY Form: {'Present' if global_academic_year_filter_form_instance else 'None'}")
        logger.debug(f"  Selected Global AY Obj: {selected_global_academic_year_obj}")
        logger.debug("--- END global_tenant_context DEBUG OUTPUT ---")
            
    return context_to_return


# # --- Main Consolidated Context Processor ---
def global_tenant_context(request):
    user = getattr(request, 'user', None)
    current_tenant_obj = getattr(request, 'tenant', None)
    
    user_for_tenant_perms = getattr(request, 'effective_tenant_user', user)

    # Initialize context dictionaries
    user_type_flags = {key: False for key in ['IS_PUBLIC_ADMIN_USER', 'IS_TENANT_STAFF_USER', 
                                            'IS_TENANT_PARENT_USER', 'IS_AUTHENTICATED_USER', 
                                            'USER_HAS_HR_PROFILE']}
    active_features = DEFAULT_ACTIVE_FEATURES.copy() # Start with defaults
    plan_limits = DEFAULT_PLAN_LIMITS.copy()
    owned_school_for_public_admin = None
    navbar_flags = {key: False for key in NAVBAR_FLAG_KEYS}
    
    is_public_schema_active = (not current_tenant_obj or current_tenant_obj.schema_name == get_public_schema_name())

    # --- 1. Populate user_type_flags ---
    if user and user.is_authenticated:
        user_type_flags['IS_AUTHENTICATED_USER'] = True
        
        if StaffUser and isinstance(user_for_tenant_perms, StaffUser):
            user_type_flags['IS_TENANT_STAFF_USER'] = True
            # Assuming EmployeeProfile is linked from StaffUser via 'employeeprofile'
            # (default reverse name for OneToOneField named 'staff_user' on EmployeeProfile)
            # and EmployeeProfile model is defined in apps.hr.models.
            try:
                # Check if the related manager exists and then if the object exists
                if hasattr(user_for_tenant_perms, 'hr_profile') and user_for_tenant_perms.employeeprofile is not None:
                    user_type_flags['USER_HAS_HR_PROFILE'] = True
                    logger.debug(f"CP: User {user_for_tenant_perms.email} HAS hr_profile.")
                else:
                    logger.debug(f"CP: User {user_for_tenant_perms.email} does NOT have hr_profile or it is None.")
            except Exception as e: 
                logger.error(f"CP: Error checking hr_profile for {user_for_tenant_perms.email}: {e}")
                pass 
        
        if ParentUser and isinstance(user, ParentUser): # request.user is the logged-in identity
            user_type_flags['IS_TENANT_PARENT_USER'] = True
        
        if isinstance(user, PublicUserModel): # request.user is an instance of settings.AUTH_USER_MODEL
            if user.is_superuser or (hasattr(user, 'is_staff') and user.is_staff): 
                user_type_flags['IS_PUBLIC_ADMIN_USER'] = True # Platform admin
            if current_tenant_obj and hasattr(current_tenant_obj, 'owner_id') and current_tenant_obj.owner_id == user.pk:
                user_type_flags['IS_PUBLIC_ADMIN_USER'] = True # Also flag owner of current tenant as such


            # --- MODIFIED SECTION FOR owned_school_for_public_admin ---
            if School and hasattr(user, 'owned_schools'): # Check if School model is available
                try:
                    # Get the first active owned school (basic info, no profile yet)
                    # This query only hits the 'public' schema's tenant table.
                    first_owned_school = user.owned_schools.filter(is_active=True).order_by('created_on').first()
                    owned_school_for_public_admin_basic = first_owned_school # Store basic school object

                    # If we are currently on that specific tenant's domain AND that tenant is the first_owned_school,
                    # then request.tenant.profile should be the one we want.
                    # Otherwise, if on public domain, we cannot reliably get the profile here without switching schema context.
                    if not is_public_schema_active and current_tenant_obj and \
                    first_owned_school and current_tenant_obj.pk == first_owned_school.pk:
                        # We are on the domain of the first owned school.
                        # Access its profile (this will query within the tenant's schema).
                        if hasattr(current_tenant_obj, 'profile'):
                            try:
                                # This assumes SchoolProfile has been created for current_tenant_obj
                                owned_school_for_public_admin_with_profile = current_tenant_obj # The school object
                                # The profile itself would be current_tenant_obj.profile
                                # Let's store the School object that has its profile eagerly loaded (if possible)
                                # or just note that we are on its page.
                            except School.profile.RelatedObjectDoesNotExist: # More specific exception
                                logger.warning(f"CP: SchoolProfile.DoesNotExist for tenant {current_tenant_obj.name} while trying to set owned_school_for_public_admin_with_profile.")
                            except AttributeError: # If 'profile' attribute doesn't exist on current_tenant_obj
                                logger.error(f"CP: current_tenant_obj (School) does not have 'profile' attribute for tenant {current_tenant_obj.name}.")

                except Exception as e:
                    logger.error(f"CP: Error fetching owned_schools for {user.email}: {e}", exc_info=settings.DEBUG)
            # --- END OF MODIFIED SECTION ---

    # --- 2. Determine Active Tenant Features & Limits ---
    if hasattr(request, 'active_tenant_features_from_middleware'): # Check for middleware-set features
        active_features.update(request.active_tenant_features_from_middleware)
    elif current_tenant_obj and current_tenant_obj.schema_name != get_public_schema_name():
        if Subscription and Feature and hasattr(current_tenant_obj, 'subscription'):
            try:
                tenant_subscription = current_tenant_obj.subscription
                if tenant_subscription and tenant_subscription.is_usable and tenant_subscription.plan:
                    plan = tenant_subscription.plan
                    if hasattr(plan, 'max_students'): plan_limits['MAX_STUDENTS'] = plan.max_students
                    if hasattr(plan, 'max_staff'): plan_limits['MAX_STAFF'] = plan.max_staff
                    
                    plan_feature_codes = set(plan.features.all().values_list('code', flat=True))
                    
                    current_features = active_features.copy() # Start with current defaults
                    for feature_code in current_features.keys():
                        if feature_code in plan_feature_codes:
                            current_features[feature_code] = True # Enable if in plan
                        else:
                            # If not in plan, disable it, UNLESS it's a core feature that should always be true
                            # This depends on how you want DEFAULT_ACTIVE_FEATURES to behave.
                            # If defaults are "always on unless plan restricts", this logic changes.
                            # Current DEFAULT_ACTIVE_FEATURES suggests some are on, some off by default.
                            # The most straightforward is: if in plan, it's on. If not, it's off, overriding default.
                            # However, some core features (like ACCOUNTING) might always be true irrespective of plan features for base functionality.
                            # For simplicity: if a feature code exists in DEFAULT_ACTIVE_FEATURES, and it's NOT in the plan,
                            # it means the plan does NOT grant it, so it should be False (unless it's a base free feature).
                            # Let's assume plan features explicitly enable things.
                            if feature_code not in plan_feature_codes:
                                active_features[feature_code] = False # If not in plan, it's off
                            # If a feature is in plan_feature_codes, it should be True, overriding any default False.
                            # This is implicitly handled if we start with False defaults for optional features.

                    # Simpler logic if DEFAULT_ACTIVE_FEATURES are truly FALLBACKS and plan is primary source
                    temp_plan_features = {key: False for key in DEFAULT_ACTIVE_FEATURES.keys()} # All off
                    for code_key in plan_feature_codes: # Enable what's in the plan
                        if code_key in temp_plan_features:
                            temp_plan_features[code_key] = True
                    # Merge with essential defaults that are always on regardless of plan
                    # For example, if 'ACCOUNTING', 'FEE_MANAGEMENT', 'STUDENT_MANAGEMENT' are always on:
                    for core_feat in ['ACCOUNTING', 'FEE_MANAGEMENT', 'STUDENT_MANAGEMENT', 'REPORTS_BASIC']:
                        if core_feat in temp_plan_features: # Ensure key exists
                            temp_plan_features[core_feat] = True
                    active_features = temp_plan_features

            except Exception as e:
                logger.error(f"CP: Error processing subscription for tenant '{current_tenant_obj.name}': {e}", exc_info=settings.DEBUG)
                # Fallback to defaults if subscription processing fails
                active_features = DEFAULT_ACTIVE_FEATURES.copy() 
    # If public schema or no tenant, defaults from active_features = DEFAULT_ACTIVE_FEATURES.copy() are used.


    # --- 3. Set Navbar Flags ---
    # Determine the user object against which permissions should be checked.
    # This could be request.user (if they are a StaffUser directly)
    # or current_tenant_obj.owner (if request.user is a PublicUserModel and is the owner of the current tenant).
    perms_to_check_on = None
    if user_type_flags['IS_TENANT_STAFF_USER']:
        perms_to_check_on = user_for_tenant_perms # This is already a StaffUser instance
    elif user_type_flags['IS_PUBLIC_ADMIN_USER'] and \
        current_tenant_obj and \
        current_tenant_obj.schema_name != get_public_schema_name() and \
        current_tenant_obj.owner_id == user.pk:
        # PublicUser is the owner of the current tenant.
        # We need their StaffUser representation in this tenant for permission checks.
        # This relies on the owner PublicUser having a corresponding StaffUser created
        # in the tenant schema during tenant setup (after_schema_created hook).
        if StaffUser: # Check if StaffUser model is available
            try:
                # The user_for_tenant_perms might have already been set by middleware.
                # If not, try to fetch the owner's StaffUser record.
                if not isinstance(user_for_tenant_perms, StaffUser): # If middleware didn't set it as StaffUser
                    owner_as_staff = StaffUser.objects.get(email__iexact=user.email) # Assuming email matches
                    perms_to_check_on = owner_as_staff
                    user_for_tenant_perms = owner_as_staff # Update for consistency in this context
                    user_type_flags['IS_TENANT_STAFF_USER'] = True # Act as staff for navbar
                    logger.debug(f"CP: Owner {user.email} is acting as StaffUser {owner_as_staff.email} for navbar perms.")
                else: # Middleware already set user_for_tenant_perms as StaffUser
                    perms_to_check_on = user_for_tenant_perms

            except StaffUser.DoesNotExist:
                logger.warning(f"CP: PublicUser owner {user.email} does not have a corresponding StaffUser in tenant {current_tenant_obj.schema_name}. Navbar may be limited.")
                # perms_to_check_on remains None, navbar flags will be mostly False
            except StaffUser.MultipleObjectsReturned:
                logger.error(f"CP: CRITICAL - Multiple StaffUser records found for email {user.email} in tenant {current_tenant_obj.schema_name}.")

    if perms_to_check_on and hasattr(perms_to_check_on, 'has_perm'):
        # Staff User or Owner-acting-as-Staff specific navbar items
        navbar_flags['show_dashboard_link'] = True 
        
        VIEW_ACCOUNT_PERM = 'accounting.view_account' # Corrected permission

        if active_features.get('STUDENT_MANAGEMENT') and perms_to_check_on.has_perm('students.view_student'):
            navbar_flags['show_manage_students_link'] = True
        
        # Fees Management Dropdown
        show_fees_dd = False
        if active_features.get('FEE_MANAGEMENT'):
            if perms_to_check_on.has_perm('fees.view_invoice'): navbar_flags['show_invoices_link'] = True; show_fees_dd = True
            if perms_to_check_on.has_perm('fees.view_concessiontype'): navbar_flags['show_concessions_link'] = True; show_fees_dd = True
            # if perms_to_check_on.has_perm('fees.view_studentfeeallocation'): navbar_flags['show_fee_allocations_link'] = True; show_fees_dd = True # Example
        if show_fees_dd: navbar_flags['show_fees_management_dropdown'] = True



            
        # Finance Dropdown
        show_finance_dd = False # Initialize flag for this dropdown
        if active_features.get('EXPENSE_TRACKING') and perms_to_check_on.has_perm('finance.view_expense'):
            navbar_flags['show_expenses_link'] = True
            show_finance_dd = True
        if active_features.get('BUDGETING') and perms_to_check_on.has_perm('finance.view_budget'):
            navbar_flags['show_budgets_link'] = True
            show_finance_dd = True
        
        if active_features.get('ACCOUNTING'): # Check main ACCOUNTING feature
            if perms_to_check_on.has_perm(VIEW_ACCOUNT_PERM): # e.g., 'accounting.view_chartofaccount'
                navbar_flags['show_chart_of_accounts_link'] = True
                show_finance_dd = True
            
            # <<< LOGIC FOR MANUAL JOURNAL ENTRIES LINK >>>
            if perms_to_check_on.has_perm('accounting.view_journalentry'):
                navbar_flags['show_manual_journal_entries_link'] = True
                show_finance_dd = True # If this link is active, the dropdown should be too
            # <<< END LOGIC FOR MANUAL JOURNAL ENTRIES LINK >>>

        if show_finance_dd:
            navbar_flags['show_finance_dropdown'] = True

        # --- HR & Staff Dropdown ---
        # Note: For superusers, HR dropdown is already enabled in the superuser section above
        # This section is for regular users who need feature-based and permission-based checks
        show_hr_dd = False

        # Staff Management (if HR_STAFF_PROFILES feature is enabled)
        if active_features.get('HR_STAFF_PROFILES', False):
            if perms_to_check_on.has_perm('schools.view_staffuser'):
                navbar_flags['show_manage_staff_users_link'] = True
                show_hr_dd = True

        # Leave Management (if HR_LEAVE_MANAGEMENT feature is enabled)
        if active_features.get('HR_LEAVE_MANAGEMENT', False):
            if perms_to_check_on.has_perm('hr.view_leaverequest'):
                navbar_flags['show_leave_management_link'] = True
                show_hr_dd = True
            if perms_to_check_on.has_perm('hr.view_leavetype'):
                navbar_flags['show_leave_type_management_link'] = True
                show_hr_dd = True
            if perms_to_check_on.has_perm('hr.add_leaverequest'):
                navbar_flags['show_staff_leave_request_link'] = True
                show_hr_dd = True

        # Set HR dropdown visibility
        if show_hr_dd:
            navbar_flags['show_hr_staff_dropdown'] = True

        # --- Reports Dropdown ---
        show_reports_dd = False
        logger.debug(f"CP: Initializing Reports. User: {getattr(perms_to_check_on, 'email', 'N/A')}")
        logger.debug(f"CP: Active Features for Reports: Basic={active_features.get('REPORTS_FIN_BASIC', False)}, Advanced={active_features.get('REPORTS_FIN_ADVANCED', False)}")

        # Superusers should have access to all reports regardless of feature flags
        is_superuser = getattr(perms_to_check_on, 'is_superuser', False)

        # --- Basic Reports ---
        if active_features.get('REPORTS_FIN_BASIC', False) or is_superuser:
            if is_superuser:
                logger.debug("CP: Evaluating Basic Reports visibility (Superuser access).")
            else:
                logger.debug("CP: Evaluating Basic Reports visibility (REPORTS_FIN_BASIC is True).")

            perm_codename = 'reporting.view_collection_report'
            if perms_to_check_on.has_perm(perm_codename):
                navbar_flags['show_collection_report_link'] = True; show_reports_dd = True
                logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_collection_report_link")
            else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_collection_report_link.")

            perm_codename = 'reporting.view_outstanding_fees_report'
            if perms_to_check_on.has_perm(perm_codename):
                navbar_flags['show_outstanding_fees_link'] = True; show_reports_dd = True
                logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_outstanding_fees_link")
            else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_outstanding_fees_link.")
                
            perm_codename = 'reporting.view_student_ledger_report'
            if perms_to_check_on.has_perm(perm_codename):
                navbar_flags['show_student_ledger_report_link'] = True; show_reports_dd = True
                logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_student_ledger_report_link")
            else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_student_ledger_report_link.")

            perm_codename = 'reporting.view_payment_summary_report'
            if perms_to_check_on.has_perm(perm_codename):
                navbar_flags['show_payment_summary_report_link'] = True; show_reports_dd = True
                logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_payment_summary_report_link")
            else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_payment_summary_report_link.")
        else:
            logger.debug("CP: REPORTS_FIN_BASIC feature is False. Skipping basic reports.")

        # --- Advanced Reports ---
        if active_features.get('REPORTS_FIN_ADVANCED', False) or is_superuser:
            if is_superuser:
                logger.debug("CP: Evaluating Advanced Reports visibility (Superuser access).")
            else:
                logger.debug("CP: Evaluating Advanced Reports visibility (REPORTS_FIN_ADVANCED is True).")

            if active_features.get('EXPENSE_TRACKING', False): # Assuming EXPENSE_TRACKING must be explicitly True
                perm_codename = 'reporting.view_expense_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_expense_report_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_expense_report_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_expense_report_link.")
            else: logger.debug("CP: EXPENSE_TRACKING feature is False. Skipping Expense Report.")

            if active_features.get('BUDGETING', False): # Assuming BUDGETING must be explicitly True
                perm_codename = 'reporting.view_budget_variance_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_budget_variance_report_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_budget_variance_report_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_budget_variance_report_link.")
            else: logger.debug("CP: BUDGETING feature is False. Skipping Budget Variance Report.")

            if active_features.get('ACCOUNTING', False): # Assuming ACCOUNTING must be explicitly True for these
                logger.debug("CP: Evaluating Accounting-related Advanced Reports (ACCOUNTING is True).")
                
                perm_codename = 'reporting.view_trial_balance_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_trial_balance_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_trial_balance_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_trial_balance_link.")

                perm_codename = 'reporting.view_income_statement_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_income_statement_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_income_statement_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_income_statement_link.")
                
                perm_codename = 'reporting.view_balance_sheet_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_balance_sheet_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_balance_sheet_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_balance_sheet_link.")

                perm_codename = 'reporting.view_cash_flow_statement_report'
                if perms_to_check_on.has_perm(perm_codename):
                    navbar_flags['show_cash_flow_link'] = True; show_reports_dd = True
                    logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_cash_flow_link")
                else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_cash_flow_link.")
            else:
                logger.debug("CP: ACCOUNTING feature is False. Skipping some advanced accounting reports.")

            perm_codename = 'reporting.view_fee_projection_report'
            if perms_to_check_on.has_perm(perm_codename):
                navbar_flags['show_fee_projection_report_link'] = True; show_reports_dd = True
                logger.debug(f"CP: User HAS perm '{perm_codename}'. Flag: show_fee_projection_report_link")
            else: logger.debug(f"CP: User LACKS perm '{perm_codename}' for show_fee_projection_report_link.")
        else:
            logger.debug("CP: REPORTS_FIN_ADVANCED feature is False. Skipping advanced reports.")

        if show_reports_dd:
            navbar_flags['show_reports_dropdown'] = True
            logger.debug("CP: Setting 'show_reports_dropdown' to True.")
        else:
            navbar_flags['show_reports_dropdown'] = False 
            logger.debug("CP: Setting 'show_reports_dropdown' to False (no report links are active).")

        # Setup/Admin Dropdown
        show_setup_dd = False
        if perms_to_check_on.has_perm('schools.change_schoolprofile'): navbar_flags['show_school_profile_link'] = True; show_setup_dd = True
        if perms_to_check_on.has_perm('schools.view_schoolclass'): navbar_flags['show_manage_classes_link'] = True; show_setup_dd = True
        if perms_to_check_on.has_perm('schools.view_academicyear'): navbar_flags['show_manage_academic_years_link'] = True; show_setup_dd = True
        if perms_to_check_on.has_perm('schools.view_term'): navbar_flags['show_manage_terms_link'] = True; show_setup_dd = True
        # if perms_to_check_on.has_perm('schools.change_academicsetting'): navbar_flags['show_manage_other_academic_settings_link'] = True; show_setup_dd = True # Example
        
        if active_features.get('ACCOUNTING') and perms_to_check_on.has_perm(VIEW_ACCOUNT_PERM) and not navbar_flags['show_chart_of_accounts_link']: # If not already in Finance dropdown
            navbar_flags['show_manage_coa_link'] = True; show_setup_dd = True
        if active_features.get('FEE_MANAGEMENT'):
            if perms_to_check_on.has_perm('fees.view_feehead'): navbar_flags['show_manage_fee_heads_link'] = True; show_setup_dd = True
            if perms_to_check_on.has_perm('fees.view_feestructure'): navbar_flags['show_manage_fee_structures_link'] = True; show_setup_dd = True
        if perms_to_check_on.has_perm('payments.view_paymentmethod'): navbar_flags['show_manage_payment_methods_link'] = True; show_setup_dd = True
        
        if perms_to_check_on.has_perm('auth.view_group'): navbar_flags['show_manage_roles_link'] = True; show_setup_dd = True # For Django Groups as Roles
        # Assuming 'portal_admin.assign_staff_roles' is a custom permission you defined
        if perms_to_check_on.has_perm('portal_admin.assign_staff_roles'): navbar_flags['show_assign_staff_to_roles_link'] = True; show_setup_dd = True
        if perms_to_check_on.has_perm('portal_admin.view_adminactivitylog'): navbar_flags['show_activity_log_link'] = True; show_setup_dd = True
        
        if show_setup_dd: navbar_flags['show_setup_admin_dropdown'] = True

    # Parent User Navbar Flags
    elif user_type_flags['IS_TENANT_PARENT_USER']:
        if active_features.get('PARENT_PORTAL'):
            navbar_flags['show_parent_dashboard_link'] = True
            navbar_flags['show_parent_children_fees_link'] = True 
            navbar_flags['show_parent_profile_link'] = True # Typically parents can view/edit their own profile
            if active_features.get('ONLINE_PAYMENTS'):
                navbar_flags['show_parent_online_payment_link'] = True
    else: 
        # User not authenticated or no perms_to_check_on
        logger.debug("CP: User not authenticated or no 'perms_to_check_on' object for setting navbar flags.")
        # All navbar_flags (including report flags) will remain False as initialized.


    # --- 4. Global Academic Year Filter Form context ---
    global_academic_year_filter_form_instance = None
    selected_global_academic_year_obj = None 
    
    # Condition to show the filter: user is authenticated AND (is tenant staff OR is owner of current tenant) AND form/models are available AND on a tenant page
    is_privileged_tenant_user = user_type_flags['IS_TENANT_STAFF_USER'] or \
                                (user_type_flags['IS_PUBLIC_ADMIN_USER'] and \
                                current_tenant_obj and \
                                hasattr(current_tenant_obj, 'owner_id') and \
                                current_tenant_obj.owner_id == user.pk)

    if user_type_flags['IS_AUTHENTICATED_USER'] and is_privileged_tenant_user and \
        GlobalAcademicYearFilterForm and AcademicYear and current_tenant_obj and \
        current_tenant_obj.schema_name != get_public_schema_name():
        
        session_key = f"selected_global_ay_pk_{current_tenant_obj.schema_name}"
        current_selected_pk_str = request.session.get(session_key)
        current_selected_pk_int = None
        if current_selected_pk_str:
            try:
                current_selected_pk_int = int(current_selected_pk_str)
            except ValueError:
                logger.warning(f"CP: Invalid non-integer AY PK '{current_selected_pk_str}' in session for key '{session_key}'.")
                if session_key in request.session: del request.session[session_key] # Clear invalid session data

        # Initialize form, possibly with value from session
        # The form's __init__ should handle filtering choices based on the tenant.
        global_academic_year_filter_form_instance = GlobalAcademicYearFilterForm(
            tenant=current_tenant_obj, 
            initial={'academic_year': current_selected_pk_int}
        )
        
        if current_selected_pk_int:
            try:
                # Fetch within current tenant's schema context
                selected_global_academic_year_obj = AcademicYear.objects.get(pk=current_selected_pk_int)
                # Optional: Further validation if AcademicYear has a direct FK to tenant
                # if hasattr(selected_global_academic_year_obj, 'tenant_id') and selected_global_academic_year_obj.tenant_id != current_tenant_obj.id:
                #    logger.warning(f"CP: AY PK {current_selected_pk_int} from session does not belong to current tenant {current_tenant_obj.name}. Clearing.")
                #    selected_global_academic_year_obj = None
                #    if session_key in request.session: del request.session[session_key]
            except AcademicYear.DoesNotExist:
                logger.warning(f"CP: Selected global AY PK {current_selected_pk_int} not found for tenant {current_tenant_obj.name}. Clearing from session.")
                if session_key in request.session: del request.session[session_key]
                selected_global_academic_year_obj = None
    
    # --- 5. Resolve current view/app info ---
    current_app_name = getattr(request.resolver_match, 'app_name', None)
    current_url_name = getattr(request.resolver_match, 'url_name', None)
    current_view_name = getattr(request.resolver_match, 'view_name', None) # Fully qualified
    
    context_to_return = {
        'user_type_flags': user_type_flags,
        'tenant_features': active_features,
        'tenant_plan_limits': plan_limits,
        'navbar_flags': navbar_flags,
        'current_tenant': current_tenant_obj,
        'owned_school_for_public_admin': owned_school_for_public_admin,
        
        'current_app_name': current_app_name,
        'current_view_name': current_view_name,
        'current_url_name': current_url_name,

        'global_academic_year_filter_form': global_academic_year_filter_form_instance,
        'selected_global_academic_year': selected_global_academic_year_obj, # Renamed
        
        'DJANGO_DEBUG': settings.DEBUG,
    }
    
    # --- Conditional Debug Logging (as per your previous version) ---
    if settings.DEBUG and current_tenant_obj and \
        (user_type_flags.get('IS_TENANT_STAFF_USER') or \
        (user_type_flags.get('IS_PUBLIC_ADMIN_USER') and hasattr(current_tenant_obj, 'owner_id') and current_tenant_obj.owner_id == user.pk)):
        
        schema_name = getattr(current_tenant_obj, 'schema_name', 'N/A')
        user_email_for_log = getattr(user_for_tenant_perms, 'email', None) or getattr(user, 'email', 'UnknownUser')

        logger.debug(f"--- global_tenant_context DEBUG OUTPUT (Tenant: {schema_name}, User: {user_email_for_log}) ---")
        logger.debug(f"  request.user: {getattr(user, 'email', 'Anonymous')}")
        logger.debug(f"  user_for_tenant_perms: {getattr(user_for_tenant_perms, 'email', 'None')}")
        logger.debug(f"  user_type_flags: {user_type_flags}")
        logger.debug(f"  active_features: {active_features}")
        logger.debug(f"  plan_limits: {plan_limits}")
        # logger.debug(f"  navbar_flags: {navbar_flags}") # Can be very long
        logger.debug(f"  navbar_flags (sorted): {dict(sorted(navbar_flags.items()))}")
        logger.debug(f"  Current App/View/URLName: {current_app_name}/{current_view_name}/{current_url_name}")
        logger.debug(f"  Global AY Form: {'Present' if global_academic_year_filter_form_instance else 'None'}")
        logger.debug(f"  Selected Global AY Obj: {selected_global_academic_year_obj}")
        logger.debug("--- END global_tenant_context DEBUG OUTPUT ---")
            
    return context_to_return





from django.utils import timezone
from django.db.models import Q # For OR conditions in filters

# Safe import for PlatformAnnouncement
PlatformAnnouncement = None
try:
    from apps.announcements.models import PlatformAnnouncement as PA_Model
    PlatformAnnouncement = PA_Model
except ImportError:
    logging.getLogger(__name__).warning("CP: PlatformAnnouncement model not found. Platform announcements will not be displayed.")
except Exception as e:
    logging.getLogger(__name__).error(f"CP Error importing PlatformAnnouncement: {e}")


def platform_announcements_processor(request):
    context_data = {}
    current_tenant_obj = getattr(request, 'tenant', None)

    # Only show platform announcements on tenant schemas, not on the public site itself
    if PlatformAnnouncement and current_tenant_obj and current_tenant_obj.schema_name != get_public_schema_name():
        now = timezone.now()
        # Get the single most recent, currently visible announcement
        latest_announcement = PlatformAnnouncement.objects.filter(
            is_published=True,
            publish_date__lte=now
        ).filter(
            Q(expiry_date__isnull=True) | Q(expiry_date__gte=now)
        ).order_by('-publish_date', '-created_at').first() # Most recent first
        
        if latest_announcement:
            context_data['latest_platform_announcement'] = latest_announcement
            # You could also pass all visible announcements if you want a list somewhere
            # context_data['all_visible_platform_announcements'] = PlatformAnnouncement.objects.filter(...)

    # Final override for superusers - ensure HR dropdown is always available
    if 'navbar_flags' in context_data and user_type_flags.get('IS_TENANT_STAFF_USER') and user and user.is_authenticated:
        # Check if user is superuser one more time and force HR flags
        if getattr(user, 'is_superuser', False):
            context_data['navbar_flags']['show_hr_staff_dropdown'] = True
            context_data['navbar_flags']['show_manage_staff_users_link'] = True
            logger.debug("CP: Final superuser override - HR dropdown and staff management enabled")

    # Debug final navbar flags
    if 'navbar_flags' in context_data:
        logger.debug(f"CP: Final navbar_flags - show_announcements_link: {context_data['navbar_flags'].get('show_announcements_link', 'NOT_SET')}")
        logger.debug(f"CP: Final navbar_flags - show_setup_admin_dropdown: {context_data['navbar_flags'].get('show_setup_admin_dropdown', 'NOT_SET')}")
        logger.debug(f"CP: Final navbar_flags - show_hr_staff_dropdown: {context_data['navbar_flags'].get('show_hr_staff_dropdown', 'NOT_SET')}")
        logger.debug(f"CP: Final navbar_flags - show_manage_staff_users_link: {context_data['navbar_flags'].get('show_manage_staff_users_link', 'NOT_SET')}")

    return context_data






