# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AdminActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('CREATE', 'Object Created'), ('UPDATE', 'Object Updated'), ('DELETE', 'Object Deleted'), ('LOGIN_SUCCESS', 'Login Success'), ('LOGIN_FAILED', 'Login Failed'), ('LOGOUT', 'Logout'), ('PASSWORD_CHANGE', 'Password Changed'), ('PASSWORD_RESET_REQUEST', 'Password Reset Requested'), ('PASSWORD_RESET_COMPLETE', 'Password Reset Completed'), ('USER_REGISTERED', 'User Registered'), ('PERMISSION_CHANGE', 'Permissions Changed for Role/Group'), ('ROLE_ASSIGNMENT', 'Role/Group Assignment to User'), ('ROLE_CREATED', 'Role/Group Created'), ('ROLE_UPDATED', 'Role/Group Updated'), ('ROLE_DELETED', 'Role/Group Deleted'), ('SCHOOL_PROFILE_UPDATE', 'School Profile Updated'), ('ACADEMIC_YEAR_SETUP', 'Academic Year/Term Setup Changed'), ('STAFF_CREATED', 'Staff Member Created'), ('STAFF_UPDATED', 'Staff Member Updated'), ('STAFF_DEACTIVATED', 'Staff Member Deactivated'), ('STAFF_REACTIVATED', 'Staff Member Reactivated'), ('LEAVE_APPLIED', 'Leave Applied'), ('LEAVE_APPROVED', 'Leave Approved'), ('LEAVE_REJECTED', 'Leave Rejected'), ('LEAVE_CANCELLED', 'Leave Cancelled'), ('STUDENT_ADMITTED', 'Student Admitted'), ('STUDENT_UPDATED', 'Student Profile Updated'), ('STUDENT_PROMOTED', 'Student Promoted/Class Changed'), ('STUDENT_WITHDRAWN', 'Student Withdrawn/Status Changed'), ('FEE_STRUCTURE_CREATED', 'Fee Structure Created'), ('FEE_STRUCTURE_UPDATED', 'Fee Structure Updated'), ('INVOICE_GENERATED', 'Invoice Generated'), ('INVOICE_SENT', 'Invoice Sent'), ('INVOICE_UPDATED', 'Invoice Updated/Adjusted'), ('INVOICE_CANCELLED', 'Invoice Cancelled'), ('PAYMENT_RECORDED', 'Payment Recorded'), ('PAYMENT_REFUNDED', 'Payment Refunded'), ('CONCESSION_APPLIED', 'Fee Concession Applied'), ('EXPENSE_RECORDED', 'Expense Recorded'), ('BUDGET_CREATED', 'Budget Created'), ('BUDGET_UPDATED', 'Budget Updated'), ('MANUAL_JE_CREATED', 'Manual Journal Entry Created'), ('SYSTEM_NOTIFICATION_SENT', 'System Notification Sent'), ('DATA_EXPORT', 'Data Exported'), ('DATA_IMPORT', 'Data Imported'), ('SETTINGS_CHANGED', 'System/School Settings Changed'), ('SYSTEM_ERROR_LOGGED', 'Significant System Error Logged'), ('GENERIC_ADMIN_ACTION', 'Generic Admin Action')], db_index=True, max_length=50, verbose_name='action type')),
                ('timestamp', models.DateTimeField(db_index=True, default=django.utils.timezone.now, verbose_name='timestamp')),
                ('actor_description', models.CharField(blank=True, help_text="Description of the actor if user/staff_user is not set (e.g., 'System Task', 'Unidentified User IP').", max_length=255, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, help_text='Browser/client user agent string.', verbose_name='user agent')),
                ('target_object_id', models.CharField(blank=True, db_index=True, max_length=255, null=True, verbose_name='target object ID')),
                ('target_object_repr', models.CharField(blank=True, help_text="A human-readable string representing the target object (e.g., 'Invoice INV-001', 'Student: John Doe').", max_length=300, null=True, verbose_name='target object representation')),
                ('description', models.TextField(blank=True, help_text="Detailed description of the event, including what changed if applicable (e.g., 'Updated status from Active to Inactive').", verbose_name='description')),
            ],
            options={
                'verbose_name': 'Admin Activity Log',
                'verbose_name_plural': 'Admin Activity Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
