# Generated by Django 5.1.9 on 2025-06-26 15:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EventCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Category Name')),
                ('color', models.CharField(default='#007bff', help_text='Hex color code for calendar display', max_length=7, verbose_name='Color')),
                ('icon', models.CharField(default='bi-calendar-event', help_text='Bootstrap icon class', max_length=50, verbose_name='Icon')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.<PERSON><PERSON>anField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Event Category',
                'verbose_name_plural': 'Event Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SchoolEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Event Title')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('event_type', models.CharField(choices=[('ACADEMIC', 'Academic'), ('SPORTS', 'Sports'), ('CULTURAL', 'Cultural'), ('MEETING', 'Meeting'), ('HOLIDAY', 'Holiday'), ('EXAM', 'Examination'), ('PARENT', 'Parent Event'), ('STAFF', 'Staff Event'), ('OTHER', 'Other')], default='OTHER', max_length=20, verbose_name='Event Type')),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='MEDIUM', max_length=10, verbose_name='Priority')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('start_time', models.TimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='End Time')),
                ('is_all_day', models.BooleanField(default=False, verbose_name='All Day Event')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='Location')),
                ('venue_details', models.TextField(blank=True, verbose_name='Venue Details')),
                ('recurrence', models.CharField(choices=[('NONE', 'No Recurrence'), ('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('YEARLY', 'Yearly')], default='NONE', max_length=10, verbose_name='Recurrence')),
                ('recurrence_end_date', models.DateField(blank=True, null=True, verbose_name='Recurrence End Date')),
                ('is_public', models.BooleanField(default=True, help_text='Visible to all users', verbose_name='Public Event')),
                ('visible_to_parents', models.BooleanField(default=True, verbose_name='Visible to Parents')),
                ('visible_to_staff', models.BooleanField(default=True, verbose_name='Visible to Staff')),
                ('visible_to_students', models.BooleanField(default=True, verbose_name='Visible to Students')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('requires_rsvp', models.BooleanField(default=False, verbose_name='Requires RSVP')),
                ('max_attendees', models.PositiveIntegerField(blank=True, null=True, verbose_name='Maximum Attendees')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='Contact Person')),
                ('contact_email', models.EmailField(blank=True, max_length=254, verbose_name='Contact Email')),
                ('contact_phone', models.CharField(blank=True, max_length=20, verbose_name='Contact Phone')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='school_calendar.eventcategory', verbose_name='Category')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_events', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'School Event',
                'verbose_name_plural': 'School Events',
                'ordering': ['start_date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='EventAttendee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rsvp_status', models.CharField(choices=[('PENDING', 'Pending'), ('ATTENDING', 'Attending'), ('NOT_ATTENDING', 'Not Attending'), ('MAYBE', 'Maybe')], default='PENDING', max_length=15)),
                ('rsvp_date', models.DateTimeField(auto_now=True)),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendees', to='school_calendar.schoolevent')),
            ],
            options={
                'verbose_name': 'Event Attendee',
                'verbose_name_plural': 'Event Attendees',
            },
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['start_date', 'end_date'], name='school_cale_start_d_c27ace_idx'),
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['event_type'], name='school_cale_event_t_5c7941_idx'),
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['is_public', 'is_active'], name='school_cale_is_publ_162aa9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='eventattendee',
            unique_together={('event', 'user')},
        ),
    ]
