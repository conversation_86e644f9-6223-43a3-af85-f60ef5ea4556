from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from datetime import datetime, time

User = get_user_model()


class EventCategory(models.Model):
    """Categories for school events"""
    name = models.CharField(max_length=100, verbose_name=_("Category Name"))
    color = models.CharField(
        max_length=7, 
        default="#007bff",
        help_text=_("Hex color code for calendar display"),
        verbose_name=_("Color")
    )
    icon = models.CharField(
        max_length=50,
        default="bi-calendar-event",
        help_text=_("Bootstrap icon class"),
        verbose_name=_("Icon")
    )
    description = models.TextField(blank=True, verbose_name=_("Description"))
    is_active = models.BooleanField(default=True, verbose_name=_("Active"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Event Category")
        verbose_name_plural = _("Event Categories")
        ordering = ['name']

    def __str__(self):
        return self.name


class SchoolEvent(models.Model):
    """School calendar events"""
    
    EVENT_TYPES = [
        ('ACADEMIC', _('Academic')),
        ('SPORTS', _('Sports')),
        ('CULTURAL', _('Cultural')),
        ('MEETING', _('Meeting')),
        ('HOLIDAY', _('Holiday')),
        ('EXAM', _('Examination')),
        ('PARENT', _('Parent Event')),
        ('STAFF', _('Staff Event')),
        ('OTHER', _('Other')),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', _('Low')),
        ('MEDIUM', _('Medium')),
        ('HIGH', _('High')),
        ('URGENT', _('Urgent')),
    ]
    
    RECURRENCE_CHOICES = [
        ('NONE', _('No Recurrence')),
        ('DAILY', _('Daily')),
        ('WEEKLY', _('Weekly')),
        ('MONTHLY', _('Monthly')),
        ('YEARLY', _('Yearly')),
    ]

    title = models.CharField(max_length=200, verbose_name=_("Event Title"))
    description = models.TextField(blank=True, verbose_name=_("Description"))
    category = models.ForeignKey(
        EventCategory, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name=_("Category")
    )
    event_type = models.CharField(
        max_length=20, 
        choices=EVENT_TYPES, 
        default='OTHER',
        verbose_name=_("Event Type")
    )
    priority = models.CharField(
        max_length=10, 
        choices=PRIORITY_CHOICES, 
        default='MEDIUM',
        verbose_name=_("Priority")
    )
    
    # Date and Time
    start_date = models.DateField(verbose_name=_("Start Date"))
    end_date = models.DateField(verbose_name=_("End Date"))
    start_time = models.TimeField(null=True, blank=True, verbose_name=_("Start Time"))
    end_time = models.TimeField(null=True, blank=True, verbose_name=_("End Time"))
    is_all_day = models.BooleanField(default=False, verbose_name=_("All Day Event"))
    
    # Location
    location = models.CharField(max_length=200, blank=True, verbose_name=_("Location"))
    venue_details = models.TextField(blank=True, verbose_name=_("Venue Details"))
    
    # Recurrence
    recurrence = models.CharField(
        max_length=10, 
        choices=RECURRENCE_CHOICES, 
        default='NONE',
        verbose_name=_("Recurrence")
    )
    recurrence_end_date = models.DateField(
        null=True, 
        blank=True,
        verbose_name=_("Recurrence End Date")
    )
    
    # Visibility and Permissions
    is_public = models.BooleanField(
        default=True, 
        help_text=_("Visible to all users"),
        verbose_name=_("Public Event")
    )
    visible_to_parents = models.BooleanField(
        default=True,
        verbose_name=_("Visible to Parents")
    )
    visible_to_staff = models.BooleanField(
        default=True,
        verbose_name=_("Visible to Staff")
    )
    visible_to_students = models.BooleanField(
        default=True,
        verbose_name=_("Visible to Students")
    )
    
    # Metadata
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_events',
        verbose_name=_("Created By")
    )
    created_by_staff = models.ForeignKey(
        'schools.StaffUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_events',
        verbose_name=_("Created By Staff")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, verbose_name=_("Active"))
    
    # Additional Features
    requires_rsvp = models.BooleanField(
        default=False,
        verbose_name=_("Requires RSVP")
    )
    max_attendees = models.PositiveIntegerField(
        null=True, 
        blank=True,
        verbose_name=_("Maximum Attendees")
    )
    contact_person = models.CharField(
        max_length=100, 
        blank=True,
        verbose_name=_("Contact Person")
    )
    contact_email = models.EmailField(blank=True, verbose_name=_("Contact Email"))
    contact_phone = models.CharField(
        max_length=20, 
        blank=True,
        verbose_name=_("Contact Phone")
    )

    class Meta:
        verbose_name = _("School Event")
        verbose_name_plural = _("School Events")
        ordering = ['start_date', 'start_time']
        indexes = [
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['event_type']),
            models.Index(fields=['is_public', 'is_active']),
        ]

    def __str__(self):
        return f"{self.title} - {self.start_date}"

    def get_creator_name(self):
        """Get the name of whoever created this event"""
        if self.created_by_staff:
            return self.created_by_staff.get_full_name()
        elif self.created_by:
            return f"{self.created_by.first_name} {self.created_by.last_name}".strip() or self.created_by.email
        return "Unknown"

    def clean(self):
        """Validate the event data"""
        if self.end_date < self.start_date:
            raise ValidationError(_("End date cannot be before start date"))
        
        if not self.is_all_day and self.start_time and self.end_time:
            if self.start_date == self.end_date and self.end_time <= self.start_time:
                raise ValidationError(_("End time must be after start time for same-day events"))
        
        if self.recurrence != 'NONE' and not self.recurrence_end_date:
            raise ValidationError(_("Recurrence end date is required for recurring events"))

    @property
    def duration_days(self):
        """Calculate event duration in days"""
        return (self.end_date - self.start_date).days + 1

    @property
    def is_multi_day(self):
        """Check if event spans multiple days"""
        return self.start_date != self.end_date

    @property
    def is_upcoming(self):
        """Check if event is in the future"""
        return self.start_date > timezone.now().date()

    @property
    def is_ongoing(self):
        """Check if event is currently happening"""
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date

    @property
    def is_past(self):
        """Check if event has ended"""
        return self.end_date < timezone.now().date()

    @classmethod
    def get_events_for_month(cls, year, month, user=None):
        """Get events for a specific month with user visibility filtering"""
        from datetime import date
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1)
        else:
            end_date = date(year, month + 1, 1)
        
        queryset = cls.objects.filter(
            is_active=True,
            start_date__lt=end_date,
            end_date__gte=start_date
        ).select_related('category', 'created_by')
        
        # Apply user-based filtering if user is provided
        if user:
            if hasattr(user, 'is_staff') and user.is_staff:
                queryset = queryset.filter(visible_to_staff=True)
            elif hasattr(user, 'is_parent') and user.is_parent:
                queryset = queryset.filter(visible_to_parents=True)
            else:
                queryset = queryset.filter(is_public=True)
        else:
            queryset = queryset.filter(is_public=True)
        
        return queryset

    @classmethod
    def get_upcoming_events(cls, limit=5, user=None):
        """Get upcoming events with user visibility filtering"""
        today = timezone.now().date()
        queryset = cls.objects.filter(
            is_active=True,
            start_date__gte=today
        ).select_related('category', 'created_by')
        
        # Apply user-based filtering
        if user:
            if hasattr(user, 'is_staff') and user.is_staff:
                queryset = queryset.filter(visible_to_staff=True)
            elif hasattr(user, 'is_parent') and user.is_parent:
                queryset = queryset.filter(visible_to_parents=True)
            else:
                queryset = queryset.filter(is_public=True)
        else:
            queryset = queryset.filter(is_public=True)
        
        return queryset[:limit]


class EventAttendee(models.Model):
    """Track event attendees and RSVPs"""
    
    RSVP_STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('ATTENDING', _('Attending')),
        ('NOT_ATTENDING', _('Not Attending')),
        ('MAYBE', _('Maybe')),
    ]

    event = models.ForeignKey(
        SchoolEvent, 
        on_delete=models.CASCADE,
        related_name='attendees'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rsvp_status = models.CharField(
        max_length=15, 
        choices=RSVP_STATUS_CHOICES, 
        default='PENDING'
    )
    rsvp_date = models.DateTimeField(auto_now=True)
    notes = models.TextField(blank=True, verbose_name=_("Notes"))

    class Meta:
        unique_together = ['event', 'user']
        verbose_name = _("Event Attendee")
        verbose_name_plural = _("Event Attendees")
        
        permissions = [
            ('view_announcements_module', _('Can view the main Announcements module link')),
        ]

    def __str__(self):
        return f"{self.user} - {self.event.title} ({self.rsvp_status})"


