# D:\school_fees_saas_v2\apps\tenants\management\commands\create_tenant_dev.py

import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from django.conf import settings

# Import all necessary models
from apps.tenants.models import School, Domain
from apps.users.models import User  # The platform user model
from apps.subscriptions.models import SubscriptionPlan, Subscription

class Command(BaseCommand):
    help = 'Creates a new tenant, domain, owner, and subscription for development purposes.'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help="The schema name and subdomain for the tenant (e.g., 'alpha').")
        parser.add_argument('school_name', type=str, help="The full name of the school (e.g., 'Danhamombe High School').")
        parser.add_argument('owner_email', type=str, help="The email for the school's owner/admin account.")
        parser.add_argument('owner_password', type=str, help="The password for the owner's account.")
        parser.add_argument('plan_id', type=int, help="The ID of the SubscriptionPlan to assign to this tenant.")

    @transaction.atomic
    def handle(self, *args, **options):
        schema_name = options['schema_name']
        school_name = options['school_name']
        owner_email = options['owner_email']
        owner_password = options['owner_password']
        plan_id = options['plan_id']

        # --- Validations ---
        if Domain.objects.filter(domain__startswith=schema_name).exists():
            raise CommandError(f"A tenant with schema or domain '{schema_name}' already exists.")

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id)
            self.stdout.write(self.style.SUCCESS(f"Found subscription plan: '{plan.name}'"))
        except SubscriptionPlan.DoesNotExist:
            raise CommandError(f"SubscriptionPlan with ID '{plan_id}' does not exist.")

        # --- Get the base domain from settings ---
        # TENANT_BASE_DOMAIN is 'myapp.test:8000'
        # We need just 'myapp.test' for the domain record
        base_domain = settings.TENANT_BASE_DOMAIN.split(':')[0]
        tenant_domain = f"{schema_name}.{base_domain}"

        # --- Create School Owner (User) in Public Schema ---
        self.stdout.write(f"Creating owner account: {owner_email}...")
        try:
            # Create platform user account
            owner = User.objects.create_user(
                email=owner_email,
                password=owner_password,
                first_name=school_name.split()[0] if school_name else "School",
                last_name="Admin"
            )
            owner.is_active = True
            owner.save()
            self.stdout.write(self.style.SUCCESS("Owner account created successfully."))
        except Exception as e:
            raise CommandError(f"Failed to create owner account: {e}")


        # --- Create School (Tenant) in Public Schema ---
        # This will trigger auto_create_schema=True
        self.stdout.write(f"Creating tenant '{school_name}' with schema '{schema_name}'...")
        try:
            tenant = School.objects.create(
                schema_name=schema_name,
                name=school_name,
                owner=owner,
                # Add any other required fields for your School model
            )
            self.stdout.write(self.style.SUCCESS("Tenant object created. Schema creation initiated by django-tenants."))
        except Exception as e:
            raise CommandError(f"Failed to create tenant: {e}")


        # --- Create Domain for the Tenant ---
        self.stdout.write(f"Creating domain '{tenant_domain}'...")
        try:
            Domain.objects.create(
                domain=tenant_domain,
                tenant=tenant,
                is_primary=True
            )
            self.stdout.write(self.style.SUCCESS("Domain created successfully."))
        except Exception as e:
            raise CommandError(f"Failed to create domain: {e}")


        # --- Create the Subscription record ---
        self.stdout.write("Creating initial subscription record...")
        try:
            Subscription.objects.create(
                school=tenant,
                plan=plan,
                status='ACTIVE', # Or 'TRIALING'
                # Set trial_end_date if applicable
                # trial_end_date=timezone.now() + timezone.timedelta(days=14),
                current_period_start=timezone.now(),
                current_period_end=timezone.now() + timezone.timedelta(days=30), # Example
            )
            self.stdout.write(self.style.SUCCESS("Subscription record created successfully."))
        except Exception as e:
            raise CommandError(f"Failed to create subscription: {e}")

        self.stdout.write(self.style.SUCCESS(f"\nTenant '{school_name}' created successfully!"))
        self.stdout.write(f"Access it at: http://{tenant_domain}:8000")
        
        
        