# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentry',
            name='last_modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entries_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='reverses_entry',
            field=models.OneToOneField(blank=True, help_text='The journal entry that this entry reverses, if applicable.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reversed_by_entry', to='accounting.journalentry'),
        ),
        migrations.AddField(
            model_name='generalledger',
            name='journal_entry',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gl_entries', to='accounting.journalentry'),
        ),
        migrations.AddField(
            model_name='journalentryitem',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_items', to='accounting.account'),
        ),
        migrations.AddField(
            model_name='journalentryitem',
            name='journal_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='accounting.journalentry'),
        ),
        migrations.AddField(
            model_name='journalline',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_lines', to='accounting.account', verbose_name='Account'),
        ),
        migrations.AddField(
            model_name='journalline',
            name='journal_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='accounting.journalentry'),
        ),
        migrations.AddConstraint(
            model_name='account',
            constraint=models.UniqueConstraint(condition=models.Q(('code__isnull', False), models.Q(('code__exact', ''), _negated=True)), fields=('code',), name='unique_account_code_mptt_accounting'),
        ),
        migrations.AlterUniqueTogether(
            name='account',
            unique_together={('tenant', 'code')},
        ),
        migrations.AddIndex(
            model_name='generalledger',
            index=models.Index(fields=['transaction_date', 'account'], name='accounting__transac_a72e5f_idx'),
        ),
        migrations.AddIndex(
            model_name='generalledger',
            index=models.Index(fields=['account', 'transaction_date'], name='accounting__account_bfa2ca_idx'),
        ),
    ]
