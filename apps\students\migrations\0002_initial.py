# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0002_initial'),
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='current_section',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students_in_section', to='schools.section', verbose_name='current section'),
        ),
        migrations.AddField(
            model_name='student',
            name='parents',
            field=models.ManyToManyField(blank=True, related_name='children', to='students.parentuser', verbose_name='linked parent portal accounts'),
        ),
    ]
