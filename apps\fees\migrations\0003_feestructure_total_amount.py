# Generated by Django 5.1.9 on 2025-06-30 21:08

from decimal import Decimal
from django.db import migrations, models
from django.db import connection


def check_and_add_total_amount_field(apps, schema_editor):
    """
    Check if total_amount field exists in fees_feestructure table.
    If it doesn't exist, add it. If it exists, do nothing.
    """
    with connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='fees_feestructure'
            AND column_name='total_amount'
            AND table_schema=current_schema()
        """)

        if not cursor.fetchone():
            # Column doesn't exist, add it
            cursor.execute("""
                ALTER TABLE fees_feestructure
                ADD COLUMN total_amount NUMERIC(12,2) DEFAULT 0.00
            """)
            cursor.execute("""
                COMMENT ON COLUMN fees_feestructure.total_amount
                IS 'Total amount calculated from fee structure items'
            """)


def reverse_total_amount_field(apps, schema_editor):
    """
    Remove the total_amount field if it exists.
    """
    with connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='fees_feestructure'
            AND column_name='total_amount'
            AND table_schema=current_schema()
        """)

        if cursor.fetchone():
            # Column exists, remove it
            cursor.execute("""
                ALTER TABLE fees_feestructure
                DROP COLUMN total_amount
            """)


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0002_add_term_to_studentconcession'),
    ]

    operations = [
        migrations.RunPython(
            check_and_add_total_amount_field,
            reverse_total_amount_field,
            elidable=True,
        ),
    ]
