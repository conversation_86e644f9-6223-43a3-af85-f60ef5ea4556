# apps/core/templatetags/nav_utils.py
from django import template

register = template.Library()

@register.simple_tag(takes_context=True)
def get_active_class_for_hr_dropdown(context):
    request = context.get('request')
    if request and hasattr(request, 'resolver_match') and request.resolver_match:
        current_app = request.resolver_match.app_name
        current_view_name = request.resolver_match.view_name

        if current_app == 'hr':
            return ' active' # Note the leading space
        if current_app == 'schools' and current_view_name in [
            'schools:staff_list', 'schools:staff_create', 'schools:staff_detail',
            'schools:staff_update', 'schools:staff_delete', 'schools:assign_staff_roles'
        ]:
            return ' active' # Note the leading space
    return ''

@register.simple_tag(takes_context=True)
def get_active_class_for_hr_manage_staff(context): # Example for a dropdown item
    request = context.get('request')
    if request and hasattr(request, 'resolver_match') and request.resolver_match:
        if request.resolver_match.app_name == 'schools' and request.resolver_match.view_name in [
            'schools:staff_list', 'schools:staff_create', 'schools:staff_detail', 
            'schools:staff_update', 'schools:staff_delete', 'schools:assign_staff_roles'
        ]:
            return ' active'
    return ''

# Add more similar tags for other complex active states if needed