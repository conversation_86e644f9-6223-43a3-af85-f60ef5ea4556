# Generated by Django 5.1.9 on 2025-06-27 15:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0003_remove_school_foreign_key'),
        ('tenants', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prefix', models.CharField(default='INV-', help_text="Prefix for invoice numbers (e.g., 'INV-', 'BILL-').", max_length=10)),
                ('last_number', models.PositiveIntegerField(default=0, help_text='The last sequential number used.')),
                ('padding_digits', models.PositiveSmallIntegerField(default=5, help_text='Number of digits to pad the sequence number (e.g., 5 for 00001).')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='When this sequence was last updated.')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this sequence was created.')),
                ('tenant', models.OneToOneField(help_text='The school/tenant this sequence belongs to.', on_delete=django.db.models.deletion.CASCADE, related_name='invoice_sequence', to='tenants.school')),
            ],
            options={
                'verbose_name': 'Invoice Sequence',
                'verbose_name_plural': 'Invoice Sequences',
                'ordering': ['tenant__name'],
            },
        ),
    ]
