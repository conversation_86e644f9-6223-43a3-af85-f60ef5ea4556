# Generated by Django 5.1.9 on 2025-06-18 20:41

import apps.students.models
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ParentUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='email address')),
                ('username', models.CharField(blank=True, help_text='Optional. Used for display or internal reference if needed.', max_length=150, null=True, verbose_name='username')),
                ('first_name', models.CharField(max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(max_length=150, verbose_name='last name')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='phone number')),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True, verbose_name='address line 1')),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True, verbose_name='address line 2')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='city')),
                ('state_province', models.CharField(blank=True, max_length=100, null=True, verbose_name='state/province')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='postal/zip code')),
                ('country', models.CharField(blank=True, max_length=100, null=True, verbose_name='country')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='parent_profiles/', verbose_name='profile picture')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('is_staff', models.BooleanField(default=False, verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this parent user belongs to...', related_name='parentuser_groups_set', related_query_name='parentuser_group', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this parent user.', related_name='parentuser_permissions_set', related_query_name='parentuser_permission', to='auth.permission', verbose_name='parent user permissions')),
            ],
            options={
                'verbose_name': 'parent user',
                'verbose_name_plural': 'parent users',
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('admission_number', models.CharField(help_text='Unique admission number for the student.', max_length=50, unique=True)),
                ('first_name', models.CharField(max_length=100, verbose_name='first name')),
                ('middle_name', models.CharField(blank=True, max_length=100, verbose_name='middle name')),
                ('last_name', models.CharField(max_length=100, verbose_name='last name')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='date of birth')),
                ('gender', models.CharField(blank=True, choices=[('MALE', 'Male'), ('FEMALE', 'Female'), ('OTHER', 'Other'), ('NOT_SPECIFIED', 'Not Specified')], max_length=15, verbose_name='gender')),
                ('photo', models.ImageField(blank=True, null=True, upload_to=apps.students.models.student_photo_upload_path, verbose_name='student photo')),
                ('date_of_admission', models.DateField(default=django.utils.timezone.now, verbose_name='date of admission')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('SUSPENDED', 'Suspended'), ('GRADUATED', 'Graduated'), ('LEFT_SCHOOL', 'Left School')], default='ACTIVE', help_text='Current status of the student.', max_length=20, verbose_name='status')),
                ('is_active', models.BooleanField(default=True, help_text='Is the student currently considered active in the school?', verbose_name='is active')),
                ('roll_number', models.CharField(blank=True, help_text='Optional roll number within the class/section.', max_length=20, verbose_name='roll number')),
                ('student_email', models.EmailField(blank=True, max_length=254, verbose_name="student's email (optional)")),
                ('student_phone', models.CharField(blank=True, max_length=30, verbose_name="student's phone (optional)")),
                ('guardian1_full_name', models.CharField(blank=True, max_length=150, verbose_name='guardian 1 full name')),
                ('guardian1_relationship', models.CharField(blank=True, help_text='e.g., Father, Mother', max_length=50, verbose_name='guardian 1 relationship')),
                ('guardian1_phone', models.CharField(blank=True, max_length=30, verbose_name='guardian 1 phone')),
                ('guardian1_email', models.EmailField(blank=True, max_length=254, verbose_name='guardian 1 email')),
                ('guardian1_occupation', models.CharField(blank=True, max_length=100, verbose_name='guardian 1 occupation')),
                ('guardian2_full_name', models.CharField(blank=True, max_length=150, verbose_name='guardian 2 full name (optional)')),
                ('guardian2_relationship', models.CharField(blank=True, max_length=50, verbose_name='guardian 2 relationship')),
                ('guardian2_phone', models.CharField(blank=True, max_length=30, verbose_name='guardian 2 phone')),
                ('guardian2_email', models.EmailField(blank=True, max_length=254, verbose_name='guardian 2 email')),
                ('address_line1', models.CharField(blank=True, max_length=255, verbose_name='address line 1')),
                ('address_line2', models.CharField(blank=True, max_length=255, verbose_name='address line 2')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='city')),
                ('state_province', models.CharField(blank=True, max_length=100, verbose_name='state/province')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='postal code')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='country')),
                ('blood_group', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-'), ('UNKNOWN', 'Unknown')], default='UNKNOWN', max_length=10, verbose_name='blood group')),
                ('allergies', models.TextField(blank=True, help_text='List any known allergies.', verbose_name='allergies')),
                ('medical_conditions', models.TextField(blank=True, help_text='List any pre-existing medical conditions.', verbose_name='medical conditions')),
                ('previous_school', models.CharField(blank=True, max_length=200, verbose_name='previous school')),
                ('notes', models.TextField(blank=True, help_text='General notes about the student.', verbose_name='notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_class', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students_in_class', to='schools.schoolclass', verbose_name='current class/grade')),
            ],
            options={
                'verbose_name': 'student',
                'verbose_name_plural': 'students',
                'ordering': ['current_class__name', 'current_section__name', 'last_name', 'first_name'],
            },
        ),
    ]
