# D:\school_fees_saas_v2\apps\schools\models.py

from django.db import models
from django.db.models import Q
from django.conf import settings # For AUTH_USER_MODEL and TENANT_MODEL
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin, Group, Permission
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Import your actual tenant model (School)
from apps.tenants.models import School

from django.contrib.auth.models import Permission

# Import ChartOfAccount model from your accounting app
from apps.accounting.models import Account as ChartOfAccount, AccountType

from django.core.exceptions import ValidationError
# Assuming you have a BaseModel or will add created_at/updated_at manually


class AcademicYear(models.Model):
    name = models.CharField(
        max_length=100, 
        unique=True, 
        help_text=_("e.g., 2024-2025, 2025") # Clarified example
    )
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(
        default=False, 
        help_text=_("Is this the currently active academic year? Only one should be active.")
    )
    is_current = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True) # Added, non-nullable
    updated_at = models.DateTimeField(auto_now=True)   # Added, non-nullable

    class Meta:
        verbose_name = _("Academic Year")
        verbose_name_plural = _("Academic Years")
        ordering = ['-start_date', 'name'] # Show newest first by start_date

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_("End date must be after start date."))
        
        # Ensure only one academic year is active at a time
        if self.is_active:
            # Check if another AcademicYear (excluding the current instance if it's being updated)
            # is already marked as active.
            active_years_query = AcademicYear.objects.filter(is_active=True)
            if self.pk: # If instance is being updated, exclude itself from the check
                active_years_query = active_years_query.exclude(pk=self.pk)
            
            if active_years_query.exists():
                active_year = active_years_query.first()
                raise ValidationError(
                    _("Another academic year ('%(active_year_name)s') is already active. "
                    "Please deactivate it first, or uncheck 'is_active' for this year.") %
                    {'active_year_name': active_year.name}
                )

    # Optional: Automatically deactivate other active years when saving one as active.
    # This is often preferred for better UX.
    # def save(self, *args, **kwargs):
    #     if self.is_active:
    #         # Deactivate all other academic years before saving this one as active.
    #         AcademicYear.objects.filter(is_active=True).exclude(pk=self.pk).update(is_active=False)
    #     super().save(*args, **kwargs)


class Term(models.Model):
    name = models.CharField(
        max_length=100, 
        help_text=_("e.g., Term 1, First Semester, Quarter 3") # Clarified example
    )
    academic_year = models.ForeignKey(
        AcademicYear, 
        on_delete=models.CASCADE, # If an AcademicYear is deleted, its Terms are also deleted
        related_name='terms'     # Allows AcademicYear.terms.all()
    )
    start_date = models.DateField()
    end_date = models.DateField()
    # Consider adding an 'is_active' field here too if you need to mark terms as active/inactive
    # for data entry, independent of the academic year's active status.
    is_active = models.BooleanField(default=False, help_text=_("Is this term currently active for operations?"))

    created_at = models.DateTimeField(auto_now_add=True) # Added, non-nullable
    updated_at = models.DateTimeField(auto_now=True)   # Added, non-nullable

    class Meta:
        verbose_name = _("Term / Semester")
        verbose_name_plural = _("Terms / Semesters")
        ordering = ['academic_year__start_date', 'start_date', 'name'] # Order by year, then term start date
        unique_together = ('academic_year', 'name') # Term name must be unique within its academic year

    def __str__(self):
        return f"{self.name} ({self.academic_year.name if self.academic_year else _('N/A Year')})"

    def clean(self):
        super().clean()
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_("End date must be after start date."))
        
        if self.academic_year: # Check if academic_year is assigned
            # Ensure term dates are within the academic year's dates
            if self.start_date < self.academic_year.start_date:
                raise ValidationError(
                    _("Term start date (%(term_start)s) cannot be before its academic year's start date (%(year_start)s).") %
                    {'term_start': self.start_date, 'year_start': self.academic_year.start_date}
                )
            if self.end_date > self.academic_year.end_date:
                raise ValidationError(
                    _("Term end date (%(term_end)s) cannot be after its academic year's end date (%(year_end)s).") %
                    {'term_end': self.end_date, 'year_end': self.academic_year.end_date}
                )
        elif self.start_date or self.end_date: # If dates are set but no academic year selected
            raise ValidationError(_("An academic year must be selected if term dates are provided."))

    @property
    def is_currently_active_term(self):
        """
        Determines if this term is currently active based on today's date
        AND its parent academic year being active.
        """
        today = timezone.now().date()
        # Ensure academic_year is not None before accessing its is_active attribute
        if self.academic_year:
            return self.academic_year.is_active and self.start_date <= today <= self.end_date
        return False

# Make sure to add any other models from apps.schools.models here if they exist
# For example, StaffUser, SchoolClass, Section, SchoolProfile etc.
# from .staff_models import StaffUser # Example if in separate file
# from .class_models import SchoolClass, Section # Example  


import logging
logger = logging.getLogger(__name__)

# Define CHOICES here or in a separate choices.py and import
GENDER_CHOICES = [
    ('', '---------'),
    ('Male', 'Male'),
    ('Female', 'Female'),
    ('Other', 'Other'),
    ('Prefer_not_to_say', 'Prefer not to say'),
]
MARITAL_STATUS_CHOICES = [
    ('', '---------'),
    ('Single', 'Single'),
    ('Married', 'Married'),
    ('Divorced', 'Divorced'),
    ('Widowed', 'Widowed'),
    ('Other', 'Other'),
]
EMPLOYMENT_TYPE_CHOICES = [
    ('', '---------'),
    ('FullTime', 'Full-Time'),
    ('PartTime', 'Part-Time'),
    ('Contract', 'Contract'),
    ('Permanent', 'Permanent'),
    ('Intern', 'Intern'),
    ('Volunteer', 'Volunteer'),
]

class StaffUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError(_('The Email must be set'))
        email = self.normalize_email(email)
        
        # Ensure required fields like first_name and last_name are provided if they are in REQUIRED_FIELDS
        # For example, if 'first_name' is in REQUIRED_FIELDS but not in extra_fields:
        # if 'first_name' not in extra_fields or not extra_fields.get('first_name'):
        #     raise ValueError(_('The First Name must be set'))
        # if 'last_name' not in extra_fields or not extra_fields.get('last_name'):
        #     raise ValueError(_('The Last Name must be set'))

        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True) # Django admin access
        extra_fields.setdefault('is_superuser', True) # Django superuser status
        extra_fields.setdefault('is_active', True)

        # Ensure all fields in REQUIRED_FIELDS are present for createsuperuser
        if 'first_name' not in extra_fields: extra_fields.setdefault('first_name', 'Platform')
        if 'last_name' not in extra_fields: extra_fields.setdefault('last_name', 'Admin')
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        
        return self.create_user(email, password, **extra_fields)


class StaffUser(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(_('email address'), unique=True, help_text=_("Used for login and communication."))
    first_name = models.CharField(_('first name'), max_length=150, blank=False)
    middle_name = models.CharField(_('middle name'), max_length=100, blank=True)
    last_name = models.CharField(_('last name'), max_length=150, blank=False)

    # Employment Details
    employee_id = models.CharField(
        _('employee ID'), max_length=50, unique=True,
        blank=True, null=True,
        help_text=_("School-specific unique employee ID.")
    )
    designation = models.CharField(_('designation'), max_length=100, blank=True, help_text="Job title or role.")
    department = models.CharField(_('department'), max_length=100, blank=True, help_text="e.g., Academics, Administration, Finance.")
    employment_type = models.CharField(
        _('employment type'), max_length=50, choices=EMPLOYMENT_TYPE_CHOICES, 
        blank=True, null=True
    )
    date_hired = models.DateField(_('date hired'), null=True, blank=True, help_text="Date when employment started.")
    date_left = models.DateField(_('date left'), null=True, blank=True, help_text="Date when employment ended, if applicable.")

    # Personal Profile Details
    gender = models.CharField(_('gender'), max_length=20, choices=GENDER_CHOICES, blank=True)
    date_of_birth = models.DateField(_('date of birth'), null=True, blank=True)
    marital_status = models.CharField(_('marital status'), max_length=20, choices=MARITAL_STATUS_CHOICES, blank=True)
    
    # Contact Information
    phone_number_primary = models.CharField(_('primary phone'), max_length=30, blank=True)
    phone_number_alternate = models.CharField(_('alternate phone'), max_length=30, blank=True)
    
    # Address
    address_line1 = models.CharField(_('address line 1'), max_length=255, blank=True)
    address_line2 = models.CharField(_('address line 2'), max_length=255, blank=True, help_text="Apartment, suite, unit, building, floor, etc.")
    city = models.CharField(_('city'), max_length=100, blank=True)
    state_province = models.CharField(_('state/province'), max_length=100, blank=True)
    postal_code = models.CharField(_('postal/zip code'), max_length=20, blank=True)
    country = models.CharField(_('country'), max_length=100, blank=True)

    # Profile Photo
    photo = models.ImageField(_('profile photo'), upload_to='staff_photos/', null=True, blank=True)
    
    # Notes
    notes = models.TextField(_("internal notes"), blank=True, help_text="Internal notes about the staff member.")


    # Django User Model Compatibility Fields
    is_active = models.BooleanField(
        _('active status'), default=True,
        help_text=_('Designates whether this user account is active. Unselect this instead of deleting accounts.')
    )
    is_staff = models.BooleanField( # This field is for Django Admin access
        _('Django admin access'), default=False,
        help_text=_('Designates whether the user can log into the Django admin site. Should typically be False for regular staff.')
    )
    # is_superuser field is inherited from PermissionsMixin, usually for platform superadmins.
    # For tenant-level "super admin", rely on group/role assignments.
    date_joined = models.DateTimeField(_('date joined system'), default=timezone.now)

    # Role & Permissions Management (Using standard Django Groups for simplicity now)
    # PermissionsMixin provides 'groups' and 'user_permissions' ManyToManyFields.
    # We can override them if we need different related_names or verbose_names.
    is_owner_profile = models.BooleanField(
        _('is owner profile'),
        default=False,
        help_text=_("Designates if this staff profile is the automatically created one for the tenant owner, "
                    "linking their public account to tenant super admin rights.")
    )
    
    groups = models.ManyToManyField(
        Group,
        verbose_name=_('groups'),
        blank=True,
        help_text=_(
            'The groups this staff user belongs to. A user will get all permissions '
            'granted to each of their groups.'
        ),
        related_name="staffuser_set",  # <--- UNIQUE related_name
        related_query_name="staffuser",
    )
    user_permissions = models.ManyToManyField(
        Permission,
        verbose_name=_('staff user permissions'),
        blank=True,
        help_text=_('Specific permissions for this staff user.'),
        related_name="staffuser_set",  # <--- UNIQUE related_name
        related_query_name="staffuser",
    )
    
    objects = StaffUserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name'] 

    class Meta:
        verbose_name = _('staff member')
        verbose_name_plural = _('staff members')
        ordering = ['last_name', 'first_name', 'email']

    def __str__(self):
        full_name = self.get_full_name()
        return full_name if full_name else self.email

    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()
    
    def get_short_name(self):
        return self.first_name

    # No need for @property full_name and short_name if you have get_full_name and get_short_name methods,
    # as Django admin and other parts of Django expect these methods.

    # --- Custom Permission Logic using TenantRole ---
    def get_all_tenant_permissions(self):
        """
        Returns a set of 'app_label.codename' permission strings for this user,
        derived from their tenant_roles and direct Django user_permissions.
        Also respects is_superuser.
        """
        if not self.is_active:
            return set()
        
        # If is_superuser is True (PermissionsMixin), they have all permissions.
        # Note: This is_superuser is for the tenant context.
        if self.is_superuser:
            # In a tenant context, "all permissions" means all permissions for apps in TENANT_APPS
            # and typically also built-in apps like 'auth', 'contenttypes' that are part of tenant schema.
            # This is a simplification; a more robust way would be to query permissions
            # available to the current tenant's content types.
            # For now, let's assume it gives wide access within the tenant.
            # A more precise way to get all permissions in the current schema:
            # return set(f"{p.content_type.app_label}.{p.codename}" for p in Permission.objects.select_related('content_type').all())
            # For simplicity if is_superuser means "all":
            logger.warning(f"StaffUser {self.email} is_superuser=True, granting all permissions (simplified).")
            # This might be too broad. Consider what is_superuser means for a StaffUser.
            # Usually, specific roles are better.
            # If is_superuser grants all *Django* permissions, use self.has_perm for those.

        perms = set()
        # Permissions from directly assigned Django user_permissions
        # These are standard Django permissions linked to this StaffUser instance.
        # The PermissionsMixin provides self.user_permissions.all()
        for perm in self.user_permissions.select_related('content_type').all():
            perms.add(f"{perm.content_type.app_label}.{perm.codename}")

        # Permissions from assigned Django groups
        # The PermissionsMixin provides self.groups.all()
        for group in self.groups.prefetch_related('permissions__content_type').all():
            for perm in group.permissions.all():
                perms.add(f"{perm.content_type.app_label}.{perm.codename}")

        # Permissions from assigned custom tenant_roles
        # This requires 'portal_admin.TenantRole' to have a ManyToManyField to 'django.contrib.auth.models.Permission'
        if hasattr(self, 'tenant_roles'): # Check if field exists
            for role in self.tenant_roles.prefetch_related('permissions__content_type').all():
                if hasattr(role, 'permissions'): # Check if TenantRole has 'permissions' M2M
                    for perm in role.permissions.select_related('content_type').all():
                        perms.add(f"{perm.content_type.app_label}.{perm.codename}")
        return perms

    def has_perm(self, perm, obj=None):
        """
        Checks if the user has a specific permission string (e.g., 'app_label.codename').
        This method is called by Django's permission system.
        It should check both directly assigned permissions and those from groups/roles.
        """
        if not self.is_active:
            return False
        
        # Superusers have all permissions by default (from PermissionsMixin)
        if self.is_superuser:
            return True
            
        # Check against the consolidated set of permissions from all sources
        return perm in self.get_all_tenant_permissions()

    def has_module_perms(self, app_label):
        """
        Checks if the user has any permissions in the given app_label.
        This method is called by Django's permission system.
        """
        if not self.is_active:
            return False
        if self.is_superuser: # Superusers have permissions for all modules
            return True
            
        for perm_str in self.get_all_tenant_permissions():
            if perm_str.startswith(f"{app_label}."):
                return True
        return False



class Country(models.Model):
    name = models.CharField(max_length=100) # This is a CharField
    code = models.CharField(max_length=3)

    def __str__(self):
        return self.name
    


import datetime # For financial_year_start_month choices
import os
from django.db import models
from django.conf import settings # Used for ACCOUNTING_..._TYPE_CODE settings
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.core.exceptions import ValidationError # For singleton enforcement

# --- Logo Upload Path Configuration ---
# This path will be relative to each tenant's media directory if
# DEFAULT_FILE_STORAGE is 'django_tenants.files.storage.TenantFileSystemStorage'.
# Example: if MEDIA_ROOT is '/var/www/mediafiles/', and tenant is 'school_a',
# the logo would be saved to '/var/www/mediafiles/school_a/school_profile_assets/logo.png'
def school_profile_logo_upload_path(instance, filename):
    _name, extension = os.path.splitext(filename)
    # Keep filename simple as it's unique per tenant profile already
    new_filename = f"logo{extension}"
    return f'school_profile_assets/{new_filename}'



# apps/schools/models.py
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings # For STAFF_USER_MODEL example if needed for other fields

# Ensure related models are imported or use string references consistently
# from apps.accounting.models import Account # Not strictly needed for FK string refs
# from .models import AcademicYear # Not strictly needed for FK string refs if defined above or in same file

# Assuming AcademicYear is defined above this in the same models.py file
# If not, ensure 'schools.AcademicYear' string reference is used.

class SchoolProfile(models.Model):
    """
    School profile information for each tenant.
    Note: No foreign key to School model since SchoolProfile exists in tenant schemas
    while School exists in public schema. The relationship is implicit through tenant context.
    """

    # Remove the foreign key to School - use implicit tenant relationship instead
    # Each tenant schema has exactly one SchoolProfile instance
    
    # Basic Info
    school_name_override = models.CharField(
        _("School Name Override"), max_length=255, blank=True, null=True, 
        help_text=_("Optional. If you want a display name different from the registered tenant name.")
    )
    school_motto = models.CharField(_("School Motto"), max_length=255, blank=True, null=True)
    logo = models.ImageField(_("School Logo"), upload_to='school_logos/', blank=True, null=True)
    school_email = models.EmailField(_("School Email"), blank=True, null=True)
    phone_number = models.CharField(_("School Phone"), max_length=30, blank=True, null=True)
    
    # Address Info
    address_line1 = models.CharField(_("Address Line 1"), max_length=255, blank=True, null=True)
    address_line2 = models.CharField(_("Address Line 2"), max_length=255, blank=True, null=True)
    city = models.CharField(_("City"), max_length=100, blank=True, null=True)
    state_province = models.CharField(_("State/Province"), max_length=100, blank=True, null=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True, null=True)
    country_name = models.CharField(_("Country Name"), max_length=100, blank=True, null=True)

    # Default Chart of Account Entries
    default_accounts_receivable_coa = models.ForeignKey(
        'accounting.Account', 
        on_delete=models.SET_NULL, null=True, blank=True, 
        related_name='profile_ar_default', # Using '+' avoids reverse accessor if not needed
        verbose_name=_("Default Accounts Receivable Account"),
        help_text=_("Default Accounts Receivable account from CoA.")
    )
    default_cash_coa = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='profile_cash_default',
        verbose_name=_("Default Cash Account"),
        help_text=_("Default Cash on Hand account from CoA.")
    )
    default_bank_coa = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='profile_bank_default',
        verbose_name=_("Default Bank Account"),
        help_text=_("Default primary Bank account from CoA.")
    )
    default_fee_income_coa = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='profile_fee_income_default',
        verbose_name=_("Default Fee Income Account"),
        help_text=_("Default Fee Income account from CoA.")
    )
    default_discount_given_coa = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='profile_discount_default',
        verbose_name=_("Default Discount Given Account"),
        help_text=_("Default Discount Given (expense/contra-income) account from CoA.")
    )
    default_expense_coa = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='profile_expense_default',
        verbose_name=_("Default General Expense Account"),
        help_text=_("Default General Expense account from CoA.")
    )

    # Academic & Financial Settings
    FINANCIAL_YEAR_MONTH_CHOICES = [
        (1, _('January')), (2, _('February')), (3, _('March')), (4, _('April')),
        (5, _('May')), (6, _('June')), (7, _('July')), (8, _('August')),
        (9, _('September')), (10, _('October')), (11, _('November')), (12, _('December')),
    ]
    financial_year_start_month = models.PositiveSmallIntegerField(
        _("Financial Year Start Month"),
        choices=FINANCIAL_YEAR_MONTH_CHOICES, 
        default=1, # Default to January
        help_text=_("The starting month of the school's financial year.")
    )
    current_academic_year = models.ForeignKey(
        'schools.AcademicYear', # Assumes AcademicYear is in this app (schools) or referenced correctly
        on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name=_("Current Academic Year"),
        help_text=_("The current active academic year for this school's operations.")
    )
    
    currency_symbol = models.CharField(
        _("Currency Symbol"), max_length=5, blank=True, null=True, default='$'
    )
    school_name_on_reports = models.CharField(
        _("School Name on Reports"), max_length=255, blank=True, null=True, 
        help_text=_("Name to display on official reports/invoices.")
    )
    default_due_days = models.PositiveIntegerField(
        _("Default Invoice Due Days"), default=15, 
        help_text=_("Default number of days invoices are due after issue date.")
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("School Profile")
        verbose_name_plural = _("School Profile Settings") # More descriptive for a singleton
        # Add any constraints if needed, e.g., ensuring only one profile per tenant 
        # (often handled by get_or_create(pk=1) in view or signal, or a unique constraint on a tenant FK if you add one)
        permissions = [
            # Report Viewing Permissions
            ("view_outstanding_fees_report", _("Can view outstanding fees report")),
            ("view_fee_collection_report", _("Can view fee collection report")),
            # ... (add other report permissions as listed in your snippet) ...
            ("view_expense_report", _("Can view expense report")),
            ("view_revenue_report", _("Can view revenue report")),
            # Management Permissions
            ("manage_school_profile", _("Can manage school profile settings")), # Simpler permission name
            ("manage_academic_settings", _("Can manage academic year and term settings")), # Broader
        ]

    def __str__(self):
        # Using self.pk as a fallback if schema name isn't readily available or for unsaved instances
        # In a tenant context, request.tenant.schema_name is better if available
        # For a model __str__, self._state.db might not always be reliable or what you want.
        # If this model is always fetched within a tenant context, you might pass tenant to it
        # or just use a generic identifier.
        return f"School Profile (ID: {self.pk or 'Unsaved'})"

    def save(self, *args, **kwargs):
        # Enforce singleton per schema (if not using a direct Tenant FK)
        # This assumes SchoolProfile is specific to the current schema.
        # It's often better to link SchoolProfile via OneToOneField to your Tenant model
        # for explicit association and easier querying.
        # If using django-tenants, the schema context should limit .objects.
        # Temporarily disable singleton check during migration
        # if not self.pk and SchoolProfile.objects.exists(): # Check within current schema
        #     raise ValidationError(_("A SchoolProfile already exists for this school. Please edit the existing one."))
        super().save(*args, **kwargs)
        
    def get_full_address(self):
        parts = [
            self.address_line1, 
            self.address_line2, 
            self.city, 
            self.state_province, 
            self.postal_code, 
            self.country_name # Corrected from self.country
        ]
        return ", ".join(filter(None, parts)) # filter(None, parts) removes empty/None strings



# --- School Class & Section ---

class SchoolClass(models.Model):
    """
    Represents a class or grade level within a school.
    e.g., Grade 1, Form 2, Nursery, Standard X.
    """
    name = models.CharField(
        _("class name"),
        max_length=100,
        help_text=_("The name of the class (e.g., Grade 1, Form IV A, Senior KG).")
        # unique=True is good if class names must be unique *within the tenant*.
        # If sections handle uniqueness (e.g. Grade 1A, Grade 1B), then name itself might not need to be unique.
        # However, for a "class" like "Grade 1", it's often unique.
    )
    description = models.TextField(
        _("description"),
        blank=True,
        null=True,
        help_text=_("Optional: A brief description of the class or its focus.")
    )
    
    is_active = models.BooleanField(
        _("is active"),
        default=True,
        db_index=True, # Good to index if you filter by this often
        help_text=_("Is this class currently active and can have students enrolled or be used in new records?")
    )

    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("last updated at"), auto_now=True)

    class Meta:
        verbose_name = _("School Class")
        verbose_name_plural = _("School Classes")
        ordering = ['name'] # Or ['grade_level', 'name'] if you add grade_level
        # If class names must be unique per academic year (if you use academic_year FK):
        # unique_together = [['academic_year', 'name']]
        # If name alone should be unique within the tenant:
        unique_together = [['name']] # This assumes django-tenants handles schema-level uniqueness.
                                    # If not, and this model is shared, you'd need tenant in unique_together.
                                    # But as a tenant-specific model (in TENANT_APPS), this is fine.


    def __str__(self):
        # if self.academic_year:
        #     return f"{self.name} ({self.academic_year.name})"
        return self.name
    

    # --- ADDED/UNCOMMENTED Section Model ---
class Section(models.Model):
    name = models.CharField(max_length=50, help_text=_("e.g., A, B, Blue Group"))
    school_class = models.ForeignKey(
        SchoolClass, 
        related_name='sections', 
        on_delete=models.CASCADE,
        verbose_name=_("Class/Grade")
    )
    # --- ADDED/UNCOMMENTED class_teacher ---
    class_teacher = models.ForeignKey(
        StaffUser, 
        null=True, 
        blank=True, 
        on_delete=models.SET_NULL, 
        related_name='class_sections_taught', # Specific related_name
        verbose_name=_("Class Teacher")
    )
    # --- ADDED room_number ---
    room_number = models.CharField(
        _("Room Number/Name"),
        max_length=50, 
        blank=True,
        help_text=_("Optional: Room number or name where this section meets.")
    )

    class Meta:
        verbose_name = _("Section")
        verbose_name_plural = _("Sections")
        ordering = ['school_class__name', 'name']
        unique_together = ('name', 'school_class') # Section name unique within a class

    def __str__(self):
        return f"{self.school_class.name} - Section {self.name}"
# class Section(models.Model):
#     name = models.CharField(max_length=50, help_text="e.g., A, B, Blue Group")
#     school_class = models.ForeignKey(SchoolClass, related_name='sections', on_delete=models.CASCADE)
#     class_teacher = models.ForeignKey(StaffUser, null=True, blank=True, on_delete=models.SET_NULL, related_name='class_sections')

#     class Meta:
#         verbose_name = "Section"
#         verbose_name_plural = "Sections"
#         ordering = ['school_class__name', 'name']
#         unique_together = ('name', 'school_class') # Section name unique within a class

#     def __str__(self):
#         return f"{self.school_class.name} - Section {self.name}"


# D:\school_fees_saas_v2\apps\schools\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
# from django.conf import settings # If you need to link to TENANT_MODEL directly

# ... (Other models: StaffUser, SchoolProfile, SchoolClass, Section, AcademicYear, Term) ...

class AcademicSetting(models.Model):
    # Each school (tenant) will have one instance of these settings.
    # If using a direct OneToOneField to the TENANT_MODEL (School):
    # school = models.OneToOneField(
    #     settings.TENANT_MODEL, # This would be 'tenants.School'
    #     on_delete=models.CASCADE,
    #     primary_key=True, # Makes it the same PK as the School
    #     related_name='academic_setting'
    # )
    # OR, if you want to manage it independently within the tenant schema (simpler for now):
    # This means one record per tenant schema. We'll enforce singleton via save method or view logic.

    current_academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='+', # No reverse accessor needed from AcademicYear
        help_text=_("The currently active academic year for this school.")
    )
    # default_term_duration_days = models.PositiveIntegerField(
    #     default=90, 
    #     help_text=_("Default duration for new terms in days (e.g., 90 for a quarter).")
    # )
    GRADING_SYSTEM_CHOICES = [
        ('PERCENTAGE', _('Percentage (0-100%)')),
        ('GPA_4', _('GPA Scale (0-4.0)')),
        ('GPA_5', _('GPA Scale (0-5.0)')),
        ('LETTER', _('Letter Grades (A, B, C...)')),
        ('CUSTOM', _('Custom/Narrative')),
    ]
    grading_system = models.CharField(
        max_length=20,
        choices=GRADING_SYSTEM_CHOICES,
        default='PERCENTAGE',
        help_text=_("The primary grading system used by the school.")
    )
    # Add other settings like:
    # - Default class size
    # - Attendance tracking method
    # - Promotion criteria (text field)
    
    # Timestamps (if not using a BaseModel that provides them)
    # created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    # updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        verbose_name = _("Academic Setting")
        verbose_name_plural = _("Academic Settings")
        # constraints = [
        #     models.UniqueConstraint(fields=['id'], name='unique_academic_setting_singleton') 
        #     # This doesn't enforce singleton, a save method override is better.
        # ]


    def __str__(self):
        # Since there should only be one per tenant, this __str__ is less critical
        # but can be useful for admin.
        # Accessing tenant name here is tricky without request context.
        return _("Academic Settings for School")

    def save(self, *args, **kwargs):
        # Enforce singleton: only one AcademicSetting record per tenant schema.
        if not self.pk and AcademicSetting.objects.exists():
            # If creating a new one and one already exists, update the existing one
            # Or raise an error. For settings, updating is often preferred.
            existing = AcademicSetting.objects.first()
            self.pk = existing.pk # Hacky way to force update, better to fetch and update fields
            # A better singleton pattern involves a get_or_create in the view.
            # For now, let's assume the view handles fetching the single instance.
            pass # Let the view handle fetching the single instance or creating it if none exists.
        super().save(*args, **kwargs)

    @classmethod
    def get_instance(cls):
        # Helper method to get the singleton instance, creating if it doesn't exist.
        obj, created = cls.objects.get_or_create(pk=1) # Assuming pk=1 for the single record
        # You might want to initialize defaults if 'created'
        return obj


class InvoiceSequence(models.Model):
    """
    Model to track invoice number sequences for each tenant.
    Ensures sequential, unique invoice numbers per school.
    Note: No foreign key to School model since InvoiceSequence exists in tenant schemas
    while School exists in public schema. The relationship is implicit through tenant context.
    Each tenant schema has exactly one InvoiceSequence instance.
    """
    prefix = models.CharField(
        max_length=10,
        default='INV-',
        help_text=_("Prefix for invoice numbers (e.g., 'INV-', 'BILL-').")
    )
    last_number = models.PositiveIntegerField(
        default=0,
        help_text=_("The last sequential number used.")
    )
    padding_digits = models.PositiveSmallIntegerField(
        default=5,
        help_text=_("Number of digits to pad the sequence number (e.g., 5 for 00001).")
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        help_text=_("When this sequence was last updated.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_("When this sequence was created.")
    )

    class Meta:
        verbose_name = _("Invoice Sequence")
        verbose_name_plural = _("Invoice Sequences")

    def __str__(self):
        return f"Invoice Sequence (Last: {self.prefix}{self.last_number:0{self.padding_digits}d})"

    def get_next_number(self):
        """
        Get the next invoice number in the sequence.
        This method should be called within a transaction with select_for_update().
        """
        self.last_number += 1
        self.save(update_fields=['last_number', 'last_updated'])
        return f"{self.prefix}{self.last_number:0{self.padding_digits}d}"


class ReceiptSequence(models.Model):
    """
    Model to track receipt number sequences for each tenant.
    Ensures sequential, unique receipt numbers per school.
    Note: No foreign key to School model since ReceiptSequence exists in tenant schemas
    while School exists in public schema. The relationship is implicit through tenant context.
    Each tenant schema has exactly one ReceiptSequence instance.
    """
    prefix = models.CharField(
        max_length=10,
        default='RCT-',
        help_text=_("Prefix for receipt numbers (e.g., 'RCT-', 'RCPT-').")
    )
    last_number = models.PositiveIntegerField(
        default=0,
        help_text=_("The last sequential number used.")
    )
    padding_digits = models.PositiveSmallIntegerField(
        default=9,
        help_text=_("Number of digits to pad the sequence number (e.g., 9 for 13-digit format).")
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        help_text=_("When this sequence was last updated.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_("When this sequence was created.")
    )

    class Meta:
        verbose_name = _("Receipt Sequence")
        verbose_name_plural = _("Receipt Sequences")

    def __str__(self):
        return f"Receipt Sequence (Last: {self.prefix}{self.last_number:0{self.padding_digits}d})"

    def get_next_number(self):
        """
        Get the next receipt number in the sequence.
        This method should be called within a transaction with select_for_update().
        """
        self.last_number += 1
        self.save(update_fields=['last_number', 'last_updated'])
        return f"{self.prefix}{self.last_number:0{self.padding_digits}d}"

