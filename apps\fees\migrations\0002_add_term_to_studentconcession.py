# Generated by Django 5.1.7 on 2025-06-29 08:41

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models
from django.db import connection


def check_and_add_term_field(apps, schema_editor):
    """
    Check if term field exists in fees_studentconcession table.
    If it doesn't exist, add it. If it exists, do nothing.
    """
    with connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='fees_studentconcession'
            AND column_name='term_id'
            AND table_schema=current_schema()
        """)

        if not cursor.fetchone():
            # Column doesn't exist, add it
            cursor.execute("""
                ALTER TABLE fees_studentconcession
                ADD COLUMN term_id BIGINT NULL
            """)
            cursor.execute("""
                ALTER TABLE fees_studentconcession
                ADD CONSTRAINT fees_studentconcession_term_id_fkey
                FOREIGN KEY (term_id) REFERENCES schools_term(id)
                DEFERRABLE INITIALLY DEFERRED
            """)


def reverse_term_field(apps, schema_editor):
    """
    Remove the term field if it exists.
    """
    with connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='fees_studentconcession'
            AND column_name='term_id'
            AND table_schema=current_schema()
        """)

        if cursor.fetchone():
            # Column exists, remove it
            cursor.execute("""
                ALTER TABLE fees_studentconcession
                DROP COLUMN term_id CASCADE
            """)


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0005_seed_account_types'),
        ('fees', '0001_initial'),
        ('schools', '0004_invoicesequence'),
        ('students', '0004_alter_student_created_by'),
    ]

    operations = [
        # Safe addition of term field - checks if it exists first
        migrations.RunPython(
            check_and_add_term_field,
            reverse_term_field,
            elidable=True,
        ),
        migrations.AddField(
            model_name='invoicedetail',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='invoicedetail',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='feehead',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='feehead',
            name='income_account_link',
            field=models.ForeignKey(blank=True, limit_choices_to={'account_type__classification': 'REVENUE'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='fee_heads', to='accounting.account', verbose_name='Default Income Account'),
        ),
        migrations.AlterField(
            model_name='feehead',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='feehead',
            name='name',
            field=models.CharField(help_text="e.g., 'Tuition Fee', 'Bus Fee'", max_length=150, unique=True),
        ),
        migrations.AlterField(
            model_name='feestructure',
            name='applicable_classes',
            field=models.ManyToManyField(blank=True, related_name='fee_structures', to='schools.schoolclass'),
        ),
        migrations.AlterField(
            model_name='feestructure',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='feestructure',
            name='name',
            field=models.CharField(help_text="e.g., 'Grade 1 Fees 2024-25 Term 1'", max_length=150),
        ),
        migrations.AlterField(
            model_name='feestructureitem',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
        migrations.AlterField(
            model_name='feestructureitem',
            name='description',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='feestructureitem',
            name='is_optional',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invoices', to='schools.staffuser'),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='internal_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='invoice_number',
            field=models.CharField(blank=True, db_index=True, max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='notes_to_parent',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='status',
            field=models.CharField(choices=[('DRAFT', 'Draft'), ('SENT', 'Sent'), ('PARTIALLY_PAID', 'Partially Paid'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue'), ('VOID', 'Void'), ('CANCELLED', 'Cancelled')], db_index=True, default='DRAFT', max_length=20),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='students.student'),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='subtotal_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='total_concession_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12),
        ),
    ]
