# Generated by Django 5.1.9 on 2025-07-05 06:44

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0003_feestructure_total_amount'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeePermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_fees_module', 'Can view the main Fees Management module link')],
                'managed': False,
                'default_permissions': (),
            },
        ),
        migrations.AddField(
            model_name='feestructure',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total amount calculated from fee structure items', max_digits=12),
        ),
    ]
