# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('portal_admin', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='adminactivitylog',
            name='user',
            field=models.ForeignKey(blank=True, help_text='The public admin user (platform owner/admin) who performed the action.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='public_admin_activity_logs', to=settings.AUTH_USER_MODEL),
        ),
    ]
