from django.db import migrations

ASSET = 'ASSET'
LIABILITY = 'LIABILITY'
EQUITY = 'EQUITY'
REVENUE = 'REVENUE' # Or 'INCOME' if your model uses that
COGS = 'COGS'
EXPENSE = 'EXPENSE'
DR = 'DR' # Debit
CR = 'CR' # Credit

ACCOUNT_TYPES_TO_SEED = [
    # Asset Types - ADD A UNIQUE 'code' FOR EACH
    {"name": "Asset Control", "code": "ASSET_CTRL", "classification": ASSET, "normal_balance": DR},
    {"name": "Cash Equivalents Control", "code": "CASH_EQ_CTRL", "classification": ASSET, "normal_balance": DR},
    {"name": "Cash", "code": "CASH", "classification": ASSET, "normal_balance": DR}, # Used in settings
    {"name": "Bank Account", "code": "BANK", "classification": ASSET, "normal_balance": DR}, # Used in settings
    {"name": "Accounts Receivable Control", "code": "AR_CTRL", "classification": ASSET, "normal_balance": DR},
    {"name": "Accounts Receivable", "code": "AR", "classification": ASSET, "normal_balance": DR},
    {"name": "Inventory Asset", "code": "INV_ASSET", "classification": ASSET, "normal_balance": DR},
    {"name": "Fixed Asset", "code": "FIXED_ASSET", "classification": ASSET, "normal_balance": DR},
    {"name": "Accumulated Depreciation", "code": "ACCUM_DEPR", "classification": ASSET, "normal_balance": CR},

    # Liability Types
    {"name": "Liability Control", "code": "LIAB_CTRL", "classification": LIABILITY, "normal_balance": CR},
    {"name": "Accounts Payable Control", "code": "AP_CTRL", "classification": LIABILITY, "normal_balance": CR},
    {"name": "Accounts Payable", "code": "AP", "classification": LIABILITY, "normal_balance": CR},
    {"name": "Unearned Revenue", "code": "UNEARNED_REV", "classification": LIABILITY, "normal_balance": CR},
    {"name": "Accrued Expense Payable", "code": "ACCR_EXP_PAY", "classification": LIABILITY, "normal_balance": CR},
    {"name": "Long-Term Debt", "code": "LT_DEBT", "classification": LIABILITY, "normal_balance": CR},

    # Equity Types
    {"name": "Equity Control", "code": "EQ_CTRL", "classification": EQUITY, "normal_balance": CR},
    {"name": "Owners Equity", "code": "OWN_EQ", "classification": EQUITY, "normal_balance": CR}, # Or Capital, etc.
    {"name": "Retained Earnings", "code": "RET_EARN", "classification": EQUITY, "normal_balance": CR},
    {"name": "Owners Drawings", "code": "OWN_DRAW", "classification": EQUITY, "normal_balance": DR}, # Contra-equity
    {"name": "Current Year Earnings", "code": "CURR_YR_EARN", "classification": EQUITY, "normal_balance": CR}, # Often system calculated

    # Revenue Types
    {"name": "Revenue Control", "code": "REV_CTRL", "classification": REVENUE, "normal_balance": CR},
    {"name": "Tuition Revenue", "code": "TUIT_REV", "classification": REVENUE, "normal_balance": CR},
    {"name": "Other Fee Revenue", "code": "OTHER_FEE_REV", "classification": REVENUE, "normal_balance": CR},
    {"name": "Miscellaneous Revenue", "code": "MISC_REV", "classification": REVENUE, "normal_balance": CR},
    {"name": "Interest Revenue", "code": "INT_REV", "classification": REVENUE, "normal_balance": CR},
    {"name": "Contra Revenue (Discounts)", "code": "CONTRA_REV_DISC", "classification": REVENUE, "normal_balance": DR},

    # COGS Types
    {"name": "COGS Control", "code": "COGS_CTRL", "classification": COGS, "normal_balance": DR},
    {"name": "Cost of Goods Sold", "code": "COGS_MAIN", "classification": COGS, "normal_balance": DR},

    # Expense Types
    {"name": "Expense Control", "code": "EXP_CTRL", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Salaries Expense", "code": "SAL_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Rent Expense", "code": "RENT_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Utilities Expense", "code": "UTIL_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Supplies Expense", "code": "SUPPL_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Repair and Maintenance Expense", "code": "REP_MAINT_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Bank Charges Expense", "code": "BANK_CHG_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Insurance Expense", "code": "INS_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Depreciation Expense", "code": "DEPR_EXP", "classification": EXPENSE, "normal_balance": DR},
    {"name": "Miscellaneous Expense", "code": "MISC_EXP", "classification": EXPENSE, "normal_balance": DR},
]

def seed_account_types(apps, schema_editor):
    AccountType = apps.get_model('accounting', 'AccountType')
    db_alias = schema_editor.connection.alias
    created_count = 0
    updated_count = 0

    print("\n  Seeding Account Types...") # Add a general print

    for at_data in ACCOUNT_TYPES_TO_SEED:
        # Use code as the primary lookup key for idempotency,
        # as names might change more often than system codes.
        # Ensure 'code' is unique in your ACCOUNT_TYPES_TO_SEED list.
        if "code" not in at_data:
            print(f"SEED_DATA_ERROR: Missing 'code' for AccountType data: {at_data['name']}. Skipping.")
            continue # Skip if code is missing, essential for lookup and system use

        obj, created = AccountType.objects.using(db_alias).update_or_create(
            code=at_data["code"], # Use code for lookup
            defaults={
                "name": at_data["name"], # Name will be updated if code matches
                "classification": at_data["classification"],
                "normal_balance": at_data["normal_balance"],
                "description": at_data.get('description', ''), # Add description if you plan to use it
                # Add 'is_active': True if your AccountType model has this field and it should be True
            }
        )
        if created:
            created_count +=1
            print(f"    Created: {obj.code} - {obj.name}")
        else:
            updated_count +=1
            # print(f"    Updated/Verified: {obj.code} - {obj.name}") # Optional: too verbose if many updates
    
    if created_count > 0:
        print(f"  SEED_DATA: Created {created_count} new AccountType records.")
    if updated_count > 0 and created_count == 0: # Only print updated if no new ones (cleaner log)
        print(f"  SEED_DATA: Verified/Updated {updated_count} existing AccountType records.")
    elif updated_count > 0 and created_count > 0:
        print(f"  SEED_DATA: And Verified/Updated {updated_count} existing AccountType records.")
    if created_count == 0 and updated_count == 0 and ACCOUNT_TYPES_TO_SEED:
        print(f"  SEED_DATA: No new AccountTypes created or updated. All {len(ACCOUNT_TYPES_TO_SEED)} items in seed list already matched existing records by code.")

def reverse_seed_account_types(apps, schema_editor):
    AccountType = apps.get_model('accounting', 'AccountType')
    db_alias = schema_editor.connection.alias
    codes_to_delete = [at_data["code"] for at_data in ACCOUNT_TYPES_TO_SEED if "code" in at_data]
    deleted_count, _ = AccountType.objects.using(db_alias).filter(code__in=codes_to_delete).delete()
    if deleted_count > 0:
        print(f"\n  SEED_DATA_REVERSE: Deleted {deleted_count} seeded AccountType records based on codes.")

class Migration(migrations.Migration):
    dependencies = [
        # This should point to the LAST structural migration file for 'accounting'
        # that created the AccountType table and its fields (including 'code').
        # Example: ('accounting', '0003_accounttype_add_code_field'),
        ('accounting', '0004_initial'), # 0003_add_code_to_accounttype'), # <<< REPLACE '0003_initial' WITH THE EXACT FILENAME
                                        # OF THE MIGRATION THAT DEFINED THE AccountType TABLE WITH THE 'code' FIELD.
    ]
    operations = [
        migrations.RunPython(seed_account_types, reverse_code=reverse_seed_account_types),
    ]
