# Generated by Django 5.1.9 on 2025-07-05 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancePermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_finance_module', 'Can view the main Finance module and navbar link'), ('manage_budgets', 'Can create and manage budgets'), ('manage_expenses', 'Can record and approve expenses')],
                'managed': False,
                'default_permissions': (),
            },
        ),
    ]
