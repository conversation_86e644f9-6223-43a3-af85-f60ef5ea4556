# Generated by Django 5.1.9 on 2025-06-24 21:40

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0005_seed_account_types'),
        ('schools', '0002_initial'),
        ('students', '0003_student_created_by'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConcessionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Name of the concession (e.g., 'Sibling Discount', 'Staff Child Waiver').", max_length=150, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('PERCENTAGE', 'Percentage'), ('FIXED_AMOUNT', 'Fixed Amount')], default='PERCENTAGE', max_length=20)),
                ('value', models.DecimalField(decimal_places=2, help_text='Percentage (e.g., 10 for 10%) or Fixed Amount.', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('is_active', models.BooleanField(default=True, help_text='Is this concession type currently available?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Concession Type',
                'verbose_name_plural': 'Concession Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FeeHead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique name for the fee category (e.g., 'Tuition Fee', 'Bus Fee'). Should be generic.", max_length=150, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description.', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Is this fee head currently active and available for use?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('income_account_link', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fee_heads_debiting_this_account', to='accounting.account', verbose_name='Default Income Account (CoA)')),
            ],
            options={
                'verbose_name': 'Fee Head',
                'verbose_name_plural': 'Fee Heads',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Descriptive name (e.g., 'Grade 1 Fees 2024-25 Term 1').", max_length=150)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Is this fee structure currently in use?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='fee_structures', to='schools.academicyear')),
                ('applicable_classes', models.ManyToManyField(blank=True, help_text='Classes this fee structure applies to. Leave blank if applicable to all or defined by student group.', related_name='applicable_fee_structures', to='schools.schoolclass')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='fee_structures', to='schools.term')),
            ],
            options={
                'verbose_name': 'Fee Structure',
                'verbose_name_plural': 'Fee Structures',
                'ordering': ['-academic_year__start_date', 'term__start_date', 'name'],
                'unique_together': {('name', 'academic_year', 'term')},
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(blank=True, db_index=True, help_text='Unique invoice number, auto-generated on first save if blank.', max_length=50, unique=True)),
                ('issue_date', models.DateField(default=django.utils.timezone.now)),
                ('due_date', models.DateField()),
                ('subtotal_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Sum of all positive line item amounts.', max_digits=12)),
                ('total_concession_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Sum of all concession/discount line items or applied concessions.', max_digits=12)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SENT', 'Sent / Unpaid'), ('PARTIALLY_PAID', 'Partially Paid'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue'), ('VOID', 'Void'), ('CANCELLED', 'Cancelled')], db_index=True, default='DRAFT', max_length=20)),
                ('notes_to_parent', models.TextField(blank=True, help_text='Visible to parent on the invoice.', null=True)),
                ('internal_notes', models.TextField(blank=True, help_text='For administrative use only.', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='schools.academicyear')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invoices_staff', to='schools.staffuser')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='student_invoices', to='students.student')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='schools.term')),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'ordering': ['-issue_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('line_type', models.CharField(choices=[('FEE_ITEM', 'Fee Item / Charge'), ('CONCESSION_ITEM', 'Concession / Discount Applied')], default='FEE_ITEM', max_length=20)),
                ('description', models.CharField(help_text='Description of the charge or concession.', max_length=255)),
                ('quantity', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, help_text='Price per unit. For concessions, this might be the discount amount per unit if quantity > 1.', max_digits=10)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Total for this line (Quantity * Unit Price). Negative for concessions.', max_digits=12)),
                ('concession_type', models.ForeignKey(blank=True, help_text='Link to ConcessionType if this is a discount line.', null=True, on_delete=django.db.models.deletion.PROTECT, to='fees.concessiontype')),
                ('fee_head', models.ForeignKey(blank=True, help_text='Link to FeeHead if this is a standard charge/fee.', null=True, on_delete=django.db.models.deletion.PROTECT, to='fees.feehead')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='fees.invoice')),
            ],
            options={
                'verbose_name': 'Invoice Line Item',
                'verbose_name_plural': 'Invoice Line Items',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructureItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('description', models.CharField(blank=True, help_text='Optional override description for this item in this structure.', max_length=255, null=True)),
                ('is_optional', models.BooleanField(default=False, help_text='Can this item be opted out of by students?')),
                ('fee_head', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='structure_items', to='fees.feehead')),
                ('fee_structure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fees.feestructure')),
            ],
            options={
                'verbose_name': 'Fee Structure Item',
                'verbose_name_plural': 'Fee Structure Items',
                'ordering': ['fee_head__name'],
                'unique_together': {('fee_structure', 'fee_head')},
            },
        ),
        migrations.CreateModel(
            name='StudentConcession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, null=True)),
                ('granted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='schools.academicyear')),
                ('concession_type', models.ForeignKey(limit_choices_to={'is_active': True}, on_delete=django.db.models.deletion.PROTECT, to='fees.concessiontype')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_concessions', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applied_concessions', to='students.student')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='schools.term')),
            ],
            options={
                'verbose_name': 'Student Specific Concession',
                'verbose_name_plural': 'Student Specific Concessions',
                'ordering': ['student__last_name', '-academic_year__start_date', 'concession_type__name'],
                'unique_together': {('student', 'concession_type', 'academic_year', 'term')},
            },
        ),
        migrations.CreateModel(
            name='StudentFeeAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, help_text='Is this allocation currently active for billing?')),
                ('notes', models.TextField(blank=True, help_text="Any specific notes for this student's fee structure allocation.", null=True, verbose_name='Notes/Reason for Allocation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='student_fee_allocations', to='schools.academicyear')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_fee_allocations', to=settings.AUTH_USER_MODEL)),
                ('fee_structure', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='student_allocations', to='fees.feestructure')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_allocations', to='students.student')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='student_fee_allocations_term', to='schools.term')),
            ],
            options={
                'verbose_name': 'Student Fee Structure Allocation',
                'verbose_name_plural': 'Student Fee Structure Allocations',
                'ordering': ['student__last_name', '-academic_year__start_date', 'term__start_date'],
                'constraints': [models.UniqueConstraint(condition=models.Q(('term__isnull', False)), fields=('student', 'fee_structure', 'term'), name='unique_student_structure_term_allocation'), models.UniqueConstraint(condition=models.Q(('term__isnull', True)), fields=('student', 'fee_structure'), name='unique_student_structure_allocation_year')],
            },
        ),
    ]
