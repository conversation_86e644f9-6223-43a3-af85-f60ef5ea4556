# Generated by Django 5.1.9 on 2025-07-05 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('portal_admin', '0003_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PortalAdminPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_setup_admin_module', 'Can view the main Setup & Admin module link'), ('assign_staff_roles', 'Can assign staff members to roles (groups)')],
                'managed': False,
                'default_permissions': (),
            },
        ),
    ]
