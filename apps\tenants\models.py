# D:\school_fees_saas_v2\apps\tenants\models.py

import logging
from django.conf import settings
from django.db import models, transaction
from django.urls import reverse
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_tenants.models import TenantMixin, DomainMixin
from django_tenants.utils import get_tenant_domain_model, schema_context
from django.core.management import call_command

# Define a single logger for this module
logger = logging.getLogger(__name__)

class School(TenantMixin):
    name = models.CharField(_("School Name"), max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True, help_text=_("URL-friendly version of name. Auto-generated."))
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='owned_schools',
        verbose_name=_("School Owner")
    )
    is_active = models.BooleanField(_("Platform Active Status"), default=True, help_text=_("Controlled by subscription status."))
    created_on = models.DateTimeField(auto_now_add=True)

    # --- django-tenants settings ---
    auto_create_schema = True
    auto_drop_schema = True # Use with caution in production

    class Meta:
        verbose_name = _("School (Tenant)")
        verbose_name_plural = _("Schools (Tenants)")
        ordering = ['name']

    def __str__(self):
        return self.name

    def _generate_safe_schema_name(self, base_name):
        """Generates a database-safe schema name."""
        # Basic implementation, can be made more robust
        return base_name.lower().replace('-', '_').replace(' ', '_')

    def create_schema(self, check_if_exists=False, sync_schema=True, verbosity=1):
        """
        Override the default create_schema method to use our safe migration command.
        This handles known migration conflicts during tenant creation.
        """
        logger.info(f"Creating schema for tenant '{self.name}' (schema: '{self.schema_name}')")

        # First, create the schema using the parent method but without migrations
        super().create_schema(check_if_exists=check_if_exists, sync_schema=False, verbosity=verbosity)

        # Then use our safe migration command
        if sync_schema:
            try:
                logger.info(f"Running safe migrations for schema '{self.schema_name}'")
                call_command('migrate_tenant_safe', self.schema_name, verbosity=verbosity)
                logger.info(f"Successfully migrated schema '{self.schema_name}'")
            except Exception as e:
                logger.error(f"Failed to migrate schema '{self.schema_name}': {e}")
                # Clean up the schema if migration failed
                try:
                    self.delete_schema()
                except:
                    pass
                raise

    def save(self, *args, **kwargs):
        """
        Overrides save to auto-generate slug and schema_name.
        All tenant setup logic is moved to the after_schema_created hook.
        """
        is_new = self._state.adding
        logger.debug(f"SCHOOL SAVE: Entered for '{self.name}' (PK: {self.pk}), Is New: {is_new}")
        
        # Auto-generate slug if it's blank
        if not self.slug:
            self.slug = slugify(self.name)
            # Basic uniqueness check
            if School.objects.filter(slug=self.slug).exists():
                self.slug = f"{self.slug}-{self.pk or School.objects.count() + 1}"

        # Set schema_name from slug if not already set
        if not self.schema_name:
            self.schema_name = self._generate_safe_schema_name(self.slug)

        super().save(*args, **kwargs)
        
        # The after_schema_created hook will be called by django-tenants automatically
        # for new tenants right after this super().save() call completes and the
        # schema has been physically created in the database.

    def after_schema_created(self, **kwargs):
        """
        Hook called by django-tenants after the schema is created.
        This is the correct place to set up initial data for a new tenant.
        """
        logger.info(f"HOOK 'after_schema_created': ENTERED for tenant '{self.name}' (Schema: '{self.schema_name}')")
        
        # Use a single atomic transaction for all setup steps
        with transaction.atomic():
            # Use schema_context to ensure all queries run against the new tenant's schema
            with schema_context(self.schema_name):
                self._setup_school_profile()
                self._setup_default_roles_and_owner_staff()

    def _setup_school_profile(self):
        """Creates the default SchoolProfile for the new tenant."""
        from apps.schools.models import SchoolProfile
        profile, created = SchoolProfile.objects.get_or_create(
            defaults={'school_name_on_reports': self.name}
        )
        if created:
            logger.info(f"HOOK: CREATED SchoolProfile for '{self.name}'.")

    def _setup_default_roles_and_owner_staff(self):
        """
        Creates a 'School Administrator' role with ALL available permissions
        and creates/assigns a StaffUser for the tenant owner.
        """
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType
        from apps.schools.models import StaffUser
        
        # --- 1. Get ALL relevant permissions ---
        tenant_app_labels = [
            'schools', 'students', 'fees', 'payments', 'accounting',
            'finance', 'hr', 'reporting', 'portal_admin', 'announcements',
            'school_calendar', 'auth' # Include auth for Group/Permission management
        ]
        content_types = ContentType.objects.filter(app_label__in=tenant_app_labels)
        all_tenant_permissions = Permission.objects.filter(content_type__in=content_types)

        # --- 2. Create the "School Administrator" role and assign all permissions ---
        admin_group, created = Group.objects.get_or_create(name="School Administrator")
        admin_group.permissions.set(all_tenant_permissions)
        logger.info(f"HOOK: Set/updated 'School Administrator' role with {all_tenant_permissions.count()} permissions.")

        # --- 3. Create the StaffUser for the tenant owner ---
        if not self.owner:
            logger.warning(f"HOOK: Tenant '{self.name}' has no owner. Skipping StaffUser creation.")
            return

        staff_user_defaults = {
            'first_name': self.owner.first_name or 'School',
            'last_name': self.owner.last_name or 'Admin',
            'is_staff': True,  # Allows login to Django admin if needed
            'is_superuser': True, # Make the owner a superuser WITHIN THEIR TENANT
            'is_active': True,
            'employee_id': f'OWNER-{self.owner.pk}',
            'designation': 'School Owner',
        }
        owner_staff_user, staff_created = StaffUser.objects.get_or_create(
            email=self.owner.email,
            defaults=staff_user_defaults
        )
        if staff_created:
            owner_staff_user.set_unusable_password() # They log in via the public owner login, not staff login
            owner_staff_user.save()
            logger.info(f"HOOK: CREATED StaffUser '{owner_staff_user.email}' for owner.")
        
        # --- 4. Assign the owner's StaffUser to the admin role ---
        owner_staff_user.groups.add(admin_group)
        logger.info(f"HOOK: Added StaffUser '{owner_staff_user.email}' to '{admin_group.name}' group.")

    def get_absolute_url(self):
        """
        Returns the full URL to the tenant's main staff dashboard.
        """
        DomainModel = get_tenant_domain_model()
        try:
            primary_domain_obj = DomainModel.objects.get(tenant=self, is_primary=True)
            domain_url_part = primary_domain_obj.domain
            
            app_scheme = getattr(settings, 'APP_SCHEME', 'http')
            app_port_str = getattr(settings, 'APP_PORT_STR', None)

            full_domain = domain_url_part
            if settings.DEBUG and app_port_str and app_port_str not in ['80', '443'] and f":{app_port_str}" not in domain_url_part:
                full_domain = f"{domain_url_part}:{app_port_str}"
            
            dashboard_path = reverse('schools:dashboard')
            return f"{app_scheme}://{full_domain}{dashboard_path}"
        except DomainModel.DoesNotExist:
            logger.warning(f"School '{self.name}' has no primary domain. Cannot generate absolute URL.")
            return reverse('public_site:home')
        except Exception as e:
            logger.error(f"Error generating absolute URL for school '{self.name}': {e}", exc_info=True)
            return reverse('public_site:home')


class Domain(DomainMixin):
    # DomainMixin provides: tenant (FK to School), domain (CharField), is_primary (BooleanField)
    class Meta:
        verbose_name = _("School Domain")
        verbose_name_plural = _("School Domains")
        ordering = ['domain']
        
    def __str__(self):
        tenant_name = self.tenant.name if self.tenant else _("Unlinked Tenant")
        return f"{tenant_name} :: {self.domain}"
    
    
    
    


# # D:\school_fees_saas_v2\apps\tenants\models.py
# import logging
# import uuid # For fallback schema name in views.py (keep import here if generate_safe_schema_name is moved to models.py)
# from django.db import models
# from django_tenants.models import TenantMixin, DomainMixin
# from django.conf import settings
# from django.utils.text import slugify
# from django.utils.translation import gettext_lazy as _
# from django.urls import reverse
# from django_tenants.utils import get_tenant_domain_model

# from django.contrib.auth.models import Group, Permission
# from django.contrib.contenttypes.models import ContentType
# from django_tenants.utils import schema_context

# # Standard logger for this module
# logger = logging.getLogger(__name__)
# # Specific logger for hooks if you want to filter/handle its output differently
# logger_hook = logging.getLogger("apps.tenants.models.hooks") # More specific name

# class School(TenantMixin): # This IS your settings.TENANT_MODEL
#     name = models.CharField(_("School Name"), max_length=100, unique=True)
#     slug = models.SlugField(
#         max_length=100, unique=True, blank=True, 
#         help_text=_("URL-friendly version of name. Auto-generated if blank. Used for schema name if schema_name not set.")
#     )
#     owner = models.ForeignKey(
#         settings.AUTH_USER_MODEL, # This is 'users.User'
#         on_delete=models.PROTECT, 
#         related_name='owned_schools',
#         verbose_name=_("School Owner")
#     )
#     # is_public field is inherited from TenantMixin (defaults to False)
#     # schema_name field is inherited from TenantMixin
    
#     is_active = models.BooleanField(
#         _("Platform Active Status"), default=True, 
#         help_text=_("Whether this tenant account is active on the platform (e.g., subscription valid).")
#     )
#     created_on = models.DateTimeField(auto_now_add=True) # Renamed from created_at for consistency with TenantMixin

#     # --- django-tenants settings ---
#     auto_create_schema = True   # Create schema on new tenant save
#     auto_drop_schema = True     # Drop schema when tenant is deleted (BE CAREFUL IN PRODUCTION)
#                                 # Consider setting to False in production and handling schema deletion manually or via a soft delete.

#     class Meta:
#         verbose_name = _("School (Tenant)")
#         verbose_name_plural = _("Schools (Tenants)")
#         ordering = ['name']

#     def __str__(self):
#         return self.name
    
#     def save(self, *args, **kwargs):
#         logger_hook.debug(f"SCHOOL SAVE: Entered for '{self.name}' (PK: {self.pk}, Schema: {self.schema_name}), IsAdding: {self._state.adding}")
        
#         if not self.slug:
#             self.slug = slugify(self.name)
#             # Ensure slug uniqueness if names might not be unique before first save
#             # (Your name field is unique=True, so this simple slugify is likely fine)
#             original_slug = self.slug
#             queryset = School.objects.all()
#             if self.pk:
#                 queryset = queryset.exclude(pk=self.pk)
            
#             next_num = 2
#             while queryset.filter(slug=self.slug).exists():
#                 self.slug = f"{original_slug}-{next_num}"
#                 next_num += 1
        
#         # If schema_name is not set, use the slug (or a more robustly generated schema name)
#         if not self.schema_name:
#             # It's safer to use a generation function that ensures DB compatibility
#             # You can define generate_safe_schema_name here or import it
#             self.schema_name = self._generate_safe_schema_name_from_slug(self.slug)

#         logger_hook.debug(f"SCHOOL SAVE: Final schema_name before super().save: '{self.schema_name}' for '{self.name}'")
#         super().save(*args, **kwargs) 
#         logger_hook.debug(f"SCHOOL SAVE: Completed super().save() for '{self.name}' (PK: {self.pk})")

#     def setup_default_role(self):
#         """
#         Creates a default 'School Administrator' role with all available
#         tenant-level permissions.
#         """
#         with schema_context(self.schema_name):
#             # Get all permissions related to your tenant apps
#             # This excludes Django's internal models and public-only apps
#             tenant_app_labels = [
#                 'schools', 'students', 'fees', 'payments', 'accounting',
#                 'finance', 'hr', 'reporting', 'portal_admin', 'announcements',
#                 'school_calendar',
#             ]
            
#             content_types = ContentType.objects.filter(app_label__in=tenant_app_labels)
#             permissions = Permission.objects.filter(content_type__in=content_types)

#             # Create the group
#             admin_group, created = Group.objects.get_or_create(name="School Administrator")

#             if created:
#                 # If the group was just created, assign all the permissions to it
#                 admin_group.permissions.set(permissions)
#                 print(f"Created 'School Administrator' role with {permissions.count()} permissions for tenant {self.schema_name}.")
#             else:
#                 # If it already exists, you could optionally update its permissions
#                 print(f"'School Administrator' role already existed for tenant {self.schema_name}.")
                
                
#     # +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#     # +++ INSERTED get_absolute_url METHOD                    +++
#     # +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#     def get_absolute_url(self):
#         """
#         Returns the full URL to the tenant's main staff/admin dashboard.
#         This is useful for links from the public site (e.g., owner's school list) 
#         or from the Django admin "View on site".
#         """
#         DomainModel = get_tenant_domain_model() # Correctly gets your Domain model
#         try:
#             primary_domain_obj = DomainModel.objects.get(tenant=self, is_primary=True)
#             domain_url_part = primary_domain_obj.domain # e.g., "alpha.myapp.test"
            
#             # Get scheme and port from Django settings if available
#             # These should ideally be set based on your environment (e.g., .env file)
#             app_scheme = getattr(settings, 'APP_SCHEME', 'http')
#             app_port_str = getattr(settings, 'APP_PORT_STR', None) # Get from settings if defined

#             full_domain = domain_url_part
#             # Add port for DEBUG mode if not standard port and not already in domain_url_part
#             if settings.DEBUG and app_port_str and app_port_str not in ['80', '443']:
#                 if f":{app_port_str}" not in domain_url_part: # Check if port is already there
#                     full_domain = f"{domain_url_part}:{app_port_str}"
            
#             # Get the path to the tenant's staff dashboard
#             # Ensure 'schools:dashboard' is the correct URL name for the tenant admin/staff dashboard
#             dashboard_path = reverse('schools:dashboard') # e.g., '/portal/dashboard/'
            
#             return f"{app_scheme}://{full_domain}{dashboard_path}"

#         except DomainModel.DoesNotExist:
#             logger_tenant_model = logging.getLogger('apps.tenants.models') # Use a defined logger
#             logger_tenant_model.warning(f"School '{self.name}' (PK: {self.pk}, Schema: {self.schema_name}) has no primary domain. Cannot generate absolute URL for dashboard.")
#             # Fallback URL if no primary domain is found. 
#             # This could be the public site homepage or a specific error/info page.
#             return reverse('public_site:home') 
#         except Exception as e:
#             logger_tenant_model = logging.getLogger('apps.tenants.models')
#             logger_tenant_model.error(f"Error generating absolute URL for school '{self.name}': {e}", exc_info=True)
#             return reverse('public_site:home') # Fallback
#     # +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#     # +++ END OF INSERTED get_absolute_url METHOD             +++
#     # +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


#     def after_schema_created(self, **kwargs):
#         logger_hook.info(f"HOOK 'after_schema_created': ENTERED for tenant '{self.name}' (Schema: '{self.schema_name}')")
        
#         try:
#             # --- LOCAL IMPORTS ---
#             from apps.schools.models import SchoolProfile, StaffUser, SchoolClass, AcademicYear, Term # VERIFY AcademicYear/Term location
#             from apps.accounting.models import Account # Use your consolidated Account model
#             # Import other models if directly needed, or rely on ContentType for permissions
#             from django.contrib.auth.models import Group, Permission
#             from django.contrib.contenttypes.models import ContentType

#             logger_hook.info(f"HOOK: Creating/Verifying SchoolProfile for '{self.name}'...")
#             profile, profile_created = SchoolProfile.objects.get_or_create(
#                 defaults={
#                     'school_name_on_reports': self.name,
#                     'school_name_override': self.name
#                 } # Add other necessary defaults
#             )
#             if profile_created:
#                 logger_hook.info(f"HOOK: CREATED SchoolProfile for '{self.name}'. Profile PK: {profile.pk}")
#             else:
#                 logger_hook.info(f"HOOK: SchoolProfile for '{self.name}' (PK: {profile.pk}) already existed.")

#             logger_hook.info(f"HOOK: Creating/Verifying StaffUser for owner '{self.owner.email if self.owner else 'N/A'}' in '{self.name}'...")
#             if self.owner and hasattr(self.owner, 'email'): # Ensure owner and owner.email exist
#                 staff_user_defaults = {
#                     'first_name': self.owner.first_name or 'School',
#                     'last_name': self.owner.last_name or 'Admin',
#                     'is_staff': True,  # <<< Assuming StaffUser.is_staff for tenant admin access
#                     'is_active': True,
#                     'employee_id': f'OWNER-{self.owner.pk}', 
#                     'designation': 'School Owner/Platform Liaison',
#                 }
#                 owner_staff_user, staff_created = StaffUser.objects.get_or_create(
#                     email=self.owner.email,
#                     defaults=staff_user_defaults
#                 )
    
#                 if staff_created:
#                     owner_staff_user.set_unusable_password() 
#                     owner_staff_user.save()
#                     logger_hook.info(f"HOOK: CREATED StaffUser '{owner_staff_user.email}' (PK: {owner_staff_user.pk}) for owner.")
#                 else:
#                     logger_hook.info(f"HOOK: StaffUser for owner '{owner_staff_user.email}' (PK: {owner_staff_user.pk}) already existed and was fetched.")
                
#                 if owner_staff_user:
#                     admin_group_name = "School Administrators"
#                     admin_group, group_created = Group.objects.get_or_create(name=admin_group_name)
#                     if group_created:
#                         logger_hook.info(f"HOOK: Created group '{admin_group_name}'.")

#                     # Ensure all app_labels and model_names are lowercase and correct
#                     # Double-check ALL permissions exist (default or custom in Model.Meta)
#                     permissions_to_assign = [
#                         # School Setup & Profile
#                         ('manage_school_profile', 'schools', 'schoolprofile'), 
#                         ('view_schoolclass', 'schools', 'schoolclass'), ('add_schoolclass', 'schools', 'schoolclass'), 
#                         ('change_schoolclass', 'schools', 'schoolclass'), ('delete_schoolclass', 'schools', 'schoolclass'),
#                         ('view_academicyear', 'schools', 'academicyear'), ('add_academicyear', 'schools', 'academicyear'), 
#                         ('change_academicyear', 'schools', 'academicyear'),('delete_academicyear', 'schools', 'academicyear'), # Added delete
#                         ('view_term', 'schools', 'term'), ('add_term', 'schools', 'term'), 
#                         ('change_term', 'schools', 'term'), ('delete_term', 'schools', 'term'), # Added delete
#                         ('manage_academic_settings', 'schools', 'schoolprofile'),

#                         # Accounting
#                         ('view_account', 'accounting', 'account'), ('add_account', 'accounting', 'account'),
#                         ('change_account', 'accounting', 'account'), ('delete_account', 'accounting', 'account'),
#                         ('view_accounttype', 'accounting', 'accounttype'), # Added AccountType perms
#                         ('add_accounttype', 'accounting', 'accounttype'),
#                         ('change_accounttype', 'accounting', 'accounttype'),
#                         ('delete_accounttype', 'accounting', 'accounttype'),
#                         ('view_journalentry', 'accounting', 'journalentry'), ('add_journalentry', 'accounting', 'journalentry'), # Assuming these exist

#                         # Fees
#                         ('view_invoice', 'fees', 'invoice'), ('add_invoice', 'fees', 'invoice'), ('change_invoice', 'fees', 'invoice'), ('delete_invoice', 'fees', 'invoice'),
#                         ('view_feestructure', 'fees', 'feestructure'), ('add_feestructure', 'fees', 'feestructure'), ('change_feestructure', 'fees', 'feestructure'), ('delete_feestructure', 'fees', 'feestructure'),
#                         ('view_feehead', 'fees', 'feehead'), ('add_feehead', 'fees', 'feehead'), ('change_feehead', 'fees', 'feehead'), ('delete_feehead', 'fees', 'feehead'),
#                         ('view_concessiontype', 'fees', 'concessiontype'), ('add_concessiontype', 'fees', 'concessiontype'), ('change_concessiontype', 'fees', 'concessiontype'), ('delete_concessiontype', 'fees', 'concessiontype'),
#                         ('view_studentconcession', 'fees', 'studentconcession'), ('add_studentconcession', 'fees', 'studentconcession'), # Example
                        
#                         # Payments
#                         ('view_paymentmethod', 'payments', 'paymentmethod'), ('add_paymentmethod', 'payments', 'paymentmethod'), ('change_paymentmethod', 'payments', 'paymentmethod'),
#                         ('view_payment', 'payments', 'payment'), ('add_payment', 'payments', 'payment'), ('change_payment', 'payments', 'payment'),
                        
#                         # Students
#                         ('view_student', 'students', 'student'), ('add_student', 'students', 'student'),
#                         ('change_student', 'students', 'student'), ('delete_student', 'students', 'student'),
#                         ('view_parentuser', 'students', 'parentuser'), ('add_parentuser', 'students', 'parentuser'), # Example

#                         # Staff / HR
#                         ('view_staffuser', 'schools', 'staffuser'), ('add_staffuser', 'schools', 'staffuser'),
#                         ('change_staffuser', 'schools', 'staffuser'), ('delete_staffuser', 'schools', 'staffuser'),
#                         ('view_leaverequest', 'hr', 'leaverequest'), ('add_leaverequest', 'hr', 'leaverequest'), 
#                         ('change_leaverequest', 'hr', 'leaverequest'), # For managers/HR to approve
#                         ('view_leavetype', 'hr', 'leavetype'), ('add_leavetype', 'hr', 'leavetype'),

#                         # Finance
#                         ('view_expense', 'finance', 'expense'), ('add_expense', 'finance', 'expense'), ('change_expense', 'finance', 'expense'),
#                         ('view_expensecategory', 'finance', 'expensecategory'), ('add_expensecategory', 'finance', 'expensecategory'),
#                         ('view_budget', 'finance', 'budget'), ('add_budget', 'finance', 'budget'), ('change_budget', 'finance', 'budget'),

#                         # Reporting (Custom perms from SchoolProfile.Meta)
#                         ('view_outstanding_fees_report', 'schools', 'schoolprofile'),
#                         ('view_fee_collection_report', 'schools', 'schoolprofile'),
#                         ('view_expense_report', 'schools', 'schoolprofile'),
#                         # Add ALL other report permissions used by navbar_flags

#                         # Tenant Admin (Roles & Permissions for django.contrib.auth.models.Group)
#                         ('view_group', 'auth', 'group'), ('add_group', 'auth', 'group'),
#                         ('change_group', 'auth', 'group'), ('delete_group', 'auth', 'group'),
#                         ('view_permission', 'auth', 'permission'),
#                         # ('assign_staff_roles', 'portal_admin', 'some_model_for_this_perm') # Example if this is custom
#                     ]
                    
#                     current_permissions_on_group = set(admin_group.permissions.all())
#                     assigned_count = 0
#                     newly_assigned_count = 0

#                     for codename, app_label, model_name in permissions_to_assign:
#                         try:
#                             content_type = ContentType.objects.get(app_label=app_label, model=model_name)
#                             permission, perm_created = Permission.objects.get_or_create(
#                                 codename=codename,
#                                 content_type=content_type,
#                                 defaults={'name': _(f'Can {codename.replace("_", " ")}')} # Use _ for translation
#                             )
#                             if permission not in current_permissions_on_group:
#                                 admin_group.permissions.add(permission)
#                                 newly_assigned_count +=1
#                             assigned_count +=1
#                             if perm_created:
#                                 logger_hook.debug(f"HOOK: Created perm object '{app_label}.{codename}'.")
#                         except ContentType.DoesNotExist:
#                             logger_hook.warning(f"HOOK: ContentType for {app_label}.{model_name} not found. Cannot assign perm '{codename}'.")
#                         except Exception as e_perm:
#                             logger_hook.error(f"HOOK: Error getting/creating/assigning perm {app_label}.{codename}: {e_perm}", exc_info=True) # exc_info=True for details
                    
#                     logger_hook.info(f"HOOK: Processed {assigned_count} permissions for '{admin_group_name}' group. Newly added to group: {newly_assigned_count}.")
                    
#                     if not owner_staff_user.groups.filter(name=admin_group_name).exists():
#                         owner_staff_user.groups.add(admin_group)
#                         logger_hook.info(f"HOOK: Added StaffUser '{owner_staff_user.email}' to '{admin_group_name}' group.")
#                     else:
#                         logger_hook.info(f"HOOK: StaffUser '{owner_staff_user.email}' already in '{admin_group_name}' group.")
#                 else:
#                     logger_hook.error(f"HOOK: owner_staff_user is None for owner '{self.owner.email}'. Cannot assign to group.")
#             else:
#                 logger_hook.warning(f"HOOK: Tenant '{self.name}' has no owner. Skipping StaffUser creation and admin group setup.")
        
#         except ImportError as ie: 
#             logger_hook.critical(f"HOOK 'after_schema_created': IMPORT ERROR for tenant '{self.name}': {ie}. Check model imports.", exc_info=True)
#         except Exception as e:
#             logger_hook.critical(f"HOOK 'after_schema_created': UNHANDLED ERROR for tenant '{self.name}': {e}. Setup incomplete.", exc_info=True)
